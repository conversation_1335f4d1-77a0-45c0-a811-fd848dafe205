<?php

class AdozioniLibri {
	private $user 		= null; // username

	/**
     * @param Data $Data oggetto connessione al database, da usare per effettuare l'autenticazione
     */
	public function __construct($user) {
		/*{{{ */
		// Inizializzo l' oggetto data
		$this->user = $user;
		/*}}}*/
	}


	/**
	 * recupera la lista degli editori sulla base del filtro testuale impostato
	 * il filtro deve essere una stringa di almeno 3 caratteri e deve essere dentro il campo $filter['editori']
     * @param String $filter
	 */
	public function getListaEditoriAIE($filter = null) {
		/*{{{ */

		$previous = $this->user->data->SetDb('adozioni_libri');
		$query_filter = '';
		if (strlen($filter['editori']) >= 3) {
			$query_filter = " WHERE descrizione_editore ilike '%" . $filter['editori'] . "%' ";
		}

		$sql = "SELECT 		aie_editore.codice_editore,
							aie_editore.descrizione_editore,
							aie_editore.annullamento_logico,
							aie_editore.codice_editore_distributore

							FROM aie_editore
							" . $query_filter . "
							ORDER BY
							aie_editore.descrizione_editore";

		$query = $this->user->data->db->prepare($sql);
		$query->execute();

		$rows = $query->fetchAll(PDO::FETCH_ASSOC);

		foreach ($rows as $key => $row) {
			$results[$row['codice_editore']] = $row;
		}

		return $results;
		/*}}}*/
	}

	/**
	 * recupera la lista delle classi sulla base del filtro testuale impostato
	 * il filtro deve essere un array che contiene i seguenti valori
	 * $filter['classe']
	 * $filter['sezione']
	 * $filter['codice_meccanografico']
     * @param String $filter
	 */
	public function getclasseEditoriAIE($filter = null) {
		/*{{{ */

		$previous = $this->user->data->SetDb('adozioni_libri');

		$sql = "SELECT classe_id from aie_classe
					where
						scuola_id='" . $filter['codice_meccanografico'] . "'
						AND
						classe='" . $filter['classe'] . "'
						AND
						sezione='" . $filter['sezione'] . "'";


		$query = $this->user->data->db->prepare($sql);
		$query->execute();

		$rows = $query->fetchAll(PDO::FETCH_ASSOC);

		foreach ($rows as $key => $row) {
			$results = $row['classe_id'];
		}

		file_put_contents('/tmp/getclasseEditoriAIE.txt', print_r($rows,true), FILE_APPEND);
		file_put_contents('/tmp/filter_getclasseEditoriAIE.txt', print_r($filter,true), FILE_APPEND);
		file_put_contents('/tmp/sql_getclasseEditoriAIE.txt', print_r($sql,true), FILE_APPEND);
		return $results;
		/*}}}*/
	}


	/**
	 * recupera la lista delle materie sulla base del filtro testuale impostato
	 * il filtro deve essere una stringa di almeno 3 caratteri e deve essere dentro il campo $filter['materia']
	 * il filtro può essere anche collegato alle discipline in quel caso il campo $filter['codice_disciplina'] deve contenere il codice della disciplina (discipline materie sono 1 a molti)
     * @param String $filter
	 */
	public function getListaMaterieAIE($filter = null) {
		/*{{{ */

		$previous = $this->user->data->SetDb('adozioni_libri');
		$query_filter = '';
		if (strlen($filter['materia']) >= 3) {
			$query_filter .= " AND descrizione_1 ilike '%" . $filter['materia'] . "%' ";
		}
		if (strlen($filter['codice_disciplina']) >= 3) {
			$query_filter .= " AND aie_disciplina.codice='" . $filter['codice_disciplina'] . "' ";
		}
			$sql = "SELECT distinct
							aie_disciplina.descrizione as descrizione_disciplina

						FROM aie_materia, aie_disciplina, aie_materiadisciplina
						WHERE
								aie_materia.codice =  aie_materiadisciplina.materia_id
								AND
								aie_materiadisciplina.disciplina_id =  aie_disciplina.codice

						" . $query_filter . "
						ORDER BY
						aie_disciplina.descrizione";

/* modifica precedente che tengo per sicurezza
		$sql = "SELECT 		aie_materia.codice,
							aie_materia.descrizione_1,
							aie_materia.descrizione_2,
							aie_materia.annullamento_logico,
							aie_disciplina.descrizione as descrizione_disciplina,
							aie_disciplina.codice as codice_disciplina

							FROM aie_materia, aie_disciplina, aie_materiadisciplina
							WHERE
								aie_materia.codice =  aie_materiadisciplina.materia_id
								AND
								aie_materiadisciplina.disciplina_id =  aie_disciplina.codice

							" . $query_filter . "
							ORDER BY
							aie_materia.descrizione_1";
*/
		file_put_contents('/tmp/sql_lista_materie_aie.txt', print_r($sql,true));
		$query = $this->user->data->db->prepare($sql);
		$query->execute();
		$rows = $query->fetchAll(PDO::FETCH_ASSOC);

		foreach ($rows as $key => $row) {
			$results[] = $row;
			//modifica precedente che tengo per sicurezza
			//$results[$row['codice']] = $row;
		}

		return $results;
		/*}}}*/
	}

	/**
	 * recupera la lista delle materie sulla base del filtro testuale impostato
	 * il filtro deve essere una stringa di almeno 3 caratteri e deve essere dentro il campo $filter['materia']
	 * il filtro può essere anche collegato alle discipline in quel caso il campo $filter['codice_disciplina'] deve contenere il codice della disciplina (discipline materie sono 1 a molti)
     * @param String $filter
	 */
	public function getListaMaterieEsploseAIE($filter = null) {
		/*{{{ */

		$previous = $this->user->data->SetDb('adozioni_libri');
		$query_filter = '';
		if (strlen($filter['materia']) >= 3) {
			$query_filter .= " AND descrizione_1 ilike '%" . $filter['materia'] . "%' ";
		}
		if (strlen($filter['codice_disciplina']) >= 3) {
			$query_filter .= " AND aie_disciplina.codice='" . $filter['codice_disciplina'] . "' ";
		}

		$sql = "SELECT 		aie_materia.codice,
							aie_materia.descrizione_1,
							aie_materia.descrizione_2,
							aie_materia.annullamento_logico,
							aie_disciplina.descrizione as descrizione_disciplina,
							aie_disciplina.codice as codice_disciplina

							FROM aie_materia, aie_disciplina, aie_materiadisciplina
							WHERE
								aie_materia.codice =  aie_materiadisciplina.materia_id
								AND
								aie_materiadisciplina.disciplina_id =  aie_disciplina.codice

							" . $query_filter . "
							ORDER BY
							aie_materia.descrizione_1";

		$query = $this->user->data->db->prepare($sql);
		$query->execute();
		$rows = $query->fetchAll(PDO::FETCH_ASSOC);

		foreach ($rows as $key => $row) {
			$results[] = $row;
			//modifica precedente che tengo per sicurezza
			//$results[$row['codice']] = $row;
		}

		return $results;
		/*}}}*/
	}

	/**
	 * recupera la lista delle materie sulla base del filtro testuale impostato
	 * il filtro deve essere una stringa di almeno 3 caratteri e deve essere dentro il campo $filter['disciplina']
     * @param String $filter
	 */
	public function getListaDisciplineAIE($filter = null) {
		/*{{{ */

		$previous = $this->user->data->SetDb('adozioni_libri');
		$query_filter = '';
		if (strlen($filter['disciplina']) >= 3) {
			$query_filter = " WHERE descrizione ilike '%" . $filter['disciplina'] . "%' ";
		}

		$sql = "SELECT 		aie_disciplina.codice,
							aie_disciplina.descrizione

							FROM aie_disciplina
							" . $query_filter . "
							ORDER BY
							aie_disciplina.descrizione";

		$query = $this->user->data->db->prepare($sql);
		$query->execute();

		$rows = $query->fetchAll(PDO::FETCH_ASSOC);

		foreach ($rows as $key => $row) {
			$results[$row['codice']] = $row;
		}

		return $results;
		/*}}}*/
	}


	/**
	 * recupera la lista delle sedi scolastiche sulla base del filtro testuale impostato
	 * il filtro deve essere una stringa di almeno 3 caratteri e deve essere dentro il campo $filter['codice_sede']
     * @param String $filter
	 */
	public function getListaSediScuoleAIE($filter = null) {
		/*{{{ */

		$previous = $this->user->data->SetDb('adozioni_libri');
		$query_filter = '';
		if (strlen($filter['codice_sede']) >= 3) {
			$query_filter = " WHERE codice_ministeriale ilike '%" . $filter['codice_sede'] . "%' ";
		}

		$sql = "SELECT 		aie_sedescolastica.codice_ministeriale,
							aie_sedescolastica.nome,
							aie_sedescolastica.indirizzo,
							aie_sedescolastica.frazione,
							aie_sedescolastica.localita,
							aie_sedescolastica.cap,
							aie_sedescolastica.telefono,
							aie_sedescolastica.fax,
							aie_sedescolastica.annullamento_logico

							FROM aie_sedescolastica
							" . $query_filter . "
							ORDER BY
							aie_sedescolastica.nome";

		$query = $this->user->data->db->prepare($sql);
		$query->execute();

		file_put_contents('/tmp/sql_sedi_scuola_aie.txt', print_r($sql,true));
		$rows = $query->fetchAll(PDO::FETCH_ASSOC);

		foreach ($rows as $key => $row) {
			$results[$row['codice_ministeriale']] = $row;
		}

		return $results;
		/*}}}*/
	}


	/**
	 * recupera la lista delle opere sulla base del filtro testuale impostato
	 * il filtro può essere composto con i campi come indicato sotto e deve essere dentro il
	 * campo $filter['opere'] un sotto array con queste chiavi:
	 * $filter['opere']['codice'] per essere utilizzato deve avere almeno 3 caratteri, altrimenti viene ignorato
	 * $filter['opere']['titolo'] per essere utilizzato deve avere almeno 4 caratteri, altrimenti viene ignorato
	 * $filter['opere']['autore'] per essere utilizzato deve avere almeno 4 caratteri, altrimenti viene ignorato
	 * $filter['opere']['editore'] per essere utilizzato deve avere almeno 4 caratteri, altrimenti viene ignorato
	 * $filter['opere']['materia'] per essere utilizzato deve avere almeno 4 caratteri, altrimenti viene ignorato
	 * $filter['opere']['editore_id'] deve essere un intero maggiore di 0
	 * $filter['opere']['materia_id'] deve essere un intero maggiore di 0
     * @param String $filter
	 */
	public function getListaOpereAIE($filter = null) {
		/*{{{ */

		$previous = $this->user->data->SetDb('adozioni_libri');
		$query_filter = '';
		$query_filter_array = [];
		if (strlen($filter['opere']['codice']) >= 3) {
			$query_filter_array[] = " codice_isbn ilike '%" . $filter['opere']['codice'] . "%' ";
		}
		if (strlen($filter['opere']['titolo']) >= 4) {
			$query_filter_array[] = " titolo ilike '%" . $filter['opere']['titolo'] . "%' ";
		}
		if (strlen($filter['opere']['autore']) >= 4) {
			$query_filter_array[] = " 	(autore ilike '%" . $filter['opere']['autore'] . "%'
										or
										autore_2 ilike '%" . $filter['opere']['autore'] . "%'
										or
										autore_3 ilike '%" . $filter['opere']['autore'] . "%')";
		}
		if (strlen($filter['opere']['editore']) >= 4) {
			$query_filter_array[] = " descrizione_editore ilike '%" . $filter['opere']['editore'] . "%' ";
		}
		if (intval($filter['opere']['id_materia']) > 0) {
			$query_filter_array[] = "( 	materia_id=" . $filter['opere']['id_materia'] . "
										or
										materia_alternativa_id=" . $filter['opere']['id_materia'] . ")";
		}
		if (intval($filter['opere']['editore_id']) > 0) {
			$query_filter_array[] = " editore_id=" . $filter['opere']['editore_id'] . " ";
		}
		if (strlen($filter['opere']['materia']) >= 4) {
			$query_filter_array[] = " 	(mat1.descrizione_1 ilike '%" . $filter['opere']['materia'] . "%'
										or
										mat2.descrizione_2 ilike '%" . $filter['opere']['materia'] . "%')";
		}

		$num_filtri = count($query_filter_array);
		$cont_filtri = 1;
		if($num_filtri > 0) {
			// $query_filter = ' AND ';
			// foreach($query_filter_array as $condizione) {
			// 	$query_filter .= $condizione;
			// 	if($cont_filtri < $num_filtri){
			// 		$query_filter .= " AND ";
			// 	}
			// 	$cont_filtri++;
			// }

			$query_filter = implode(' AND ', $query_filter_array);

			$sql = "SELECT 		aie_opera.codice_prodotto,
								aie_opera.autore,
								aie_opera.autore_2,
								aie_opera.autore_3,
								aie_opera.curatore,
								aie_opera.curatore_2,
								aie_opera.curatore_3,
								aie_opera.traduttore,
								aie_opera.traduttore2,
								aie_opera.traduttore_3,
								aie_opera.titolo,
								aie_opera.numero_volumi,
								aie_opera.progressivo_volume,
								aie_opera.anno_edizione,
								aie_opera.codice_fondamentale,
								aie_opera.codice_isbn,
								aie_opera.codice_2,
								aie_opera.codice_3,
								aie_opera.codice_4,
								aie_opera.codice_5,
								aie_opera.prezzo,
								aie_opera.annullamento_logico,
								aie_opera.sottotitolo,
								aie_opera.novita_anno_corrente,
								aie_opera.novita_anno_precedente,
								aie_opera.fuori_catalogo,
								aie_opera.fascicolata,
								aie_opera.novita_2_anni_precedenti,
								aie_opera.url_copertina,
								aie_opera.editore_id,
								aie_opera.materia_id,
								aie_opera.materia_alternativa_id,
								aie_opera.modalita_tipo_id,
								aie_opera.tipo_scuola_id,
								aie_editore.descrizione_editore,
								mat1.descrizione_1 as descrizione_materia,
								mat2.descrizione_1 as descrizione_materia_alternativa
								FROM aie_opera
								LEFT JOIN aie_materia as mat1 ON aie_opera.materia_id = mat1.codice
								LEFT JOIN aie_materia as mat2 ON aie_opera.materia_alternativa_id = mat2.codice
								LEFT JOIN aie_editore ON aie_opera.editore_id = aie_editore.codice_editore
								WHERE
								" . $query_filter . "
								ORDER BY
								aie_opera.titolo";

			$query = $this->user->data->db->prepare($sql);
			$query->execute();

			$rows = $query->fetchAll(PDO::FETCH_ASSOC);

			foreach ($rows as $key => $row) {
				$results[$row['codice_prodotto']] = $row;
			}
		}
		else{
			$results['error'] = 'Nessun filtro inserito';
		}

		return $results;
		/*}}}*/
	}


	/**
	 * funzione in GET
	 * recupera tutti i prezzi di tutte le opere
     * @param String $filter
	 */
	public function getListaPrezziOpereAIE($filter = null) {
		/*{{{ */

		$previous = $this->user->data->SetDb('adozioni_libri');

		$sql = "SELECT 		aie_opera.codice_prodotto,
								aie_opera.prezzo
								FROM aie_opera";

		$query = $this->user->data->db->prepare($sql);
		$query->execute();

		$rows = $query->fetchAll(PDO::FETCH_ASSOC);

		foreach ($rows as $key => $row) {
			$results[$row['codice_prodotto']] = $row;
		}

		return $results;
		/*}}}*/
	}


	/**
	 * recupera la lista delle sedi scolastiche sulla base del filtro testuale impostato
	 * il parametro deve essere così impostato:
	 *
	 * $parameter['tipo_raggruppamento'] che può avere i seguenti valori:
	 * "RAGGRUPPA_ANNO" che farà vedere solo una riga per anno e indirizzo (ignorando le sezioni),
	 * "ESPLODI_SEZIONI" che farà vedere una riga per ogni classe
	 * Il default se non specificato è "RAGGRUPPA_ANNO"
	 *
	 * $parameter['anno_db'] che contiene il db da utilizzare
	 *
	 * $parameter['id_professore'] che contiene l'id del docente per attivare le materie
	 *
	 * $parameter['id_raggruppamento'] che contiene l'id del raggruppamento per estrarre anche le adozioni gia' presenti
     * @param String $parameter
	 */
	public function getListaClassiAdozioniAIE($parameter = null) {
		/*{{{ */

		if(strlen($parameter['anno_db']) > 0){
			$previous = $this->user->data->SetDb($parameter['anno_db']);
		}

		if(is_numeric($parameter['id_professore']) == false){
			$parameter['id_professore'] = 0;
		}

		$and_materia_in_media = "";
		$solo_materie_in_media = $this->user->data->GetParametroMastercom('AIE_SOLO_MATERIE_IN_MEDIA');
		if ($solo_materie_in_media == 'SI'){
			$and_materia_in_media = " AND in_media_pagelle != 'NV' ";
		}


		if($parameter['tipo_raggruppamento'] != "ESPLODI_SEZIONI"){
			$sql = "
				SELECT
					DISTINCT
						indirizzi_succ.descrizione AS descrizione_indirizzi,
						classi_succ.classe,
						classi_succ.id_indirizzo,
						indirizzi_succ.id_sede
					FROM
						classi_succ
						INNER JOIN indirizzi_succ ON indirizzi_succ.id_indirizzo=classi_succ.id_indirizzo
						LEFT JOIN (SELECT * FROM sedi WHERE flag_canc=0) AS sedi_es ON sedi_es.id_sede = indirizzi_succ.id_sede::integer
						LEFT JOIN (SELECT * FROM scuole WHERE flag_canc=0) AS scuole_es ON scuole_es.id_scuola = sedi_es.id_scuola::integer
					WHERE
						classi_succ.flag_canc=0
						AND
						indirizzi_succ.flag_canc=0
						AND
						id_codice_ministeriale <> 94
						AND
						tipo_indirizzo != 'CORSO'
					ORDER BY descrizione_indirizzi, classe";
/*
			$sql = "SELECT
			DISTINCT
				descrizione_indirizzi,
				classe,
				id_indirizzo
			FROM classi_complete
			WHERE id_codice_ministeriale <> 94
				AND tipo_indirizzo != 'CORSO'
			ORDER BY descrizione_indirizzi, classe";
*/
		}
		else{
			$sql = "
				SELECT
					DISTINCT
						indirizzi_succ.descrizione AS descrizione_indirizzi,
						classi_succ.classe,
						classi_succ.sezione,
						classi_succ.id_classe,
						classi_succ.id_indirizzo,
						indirizzi_succ.id_sede
					FROM
						classi_succ
						INNER JOIN indirizzi_succ ON indirizzi_succ.id_indirizzo=classi_succ.id_indirizzo
						LEFT JOIN (SELECT * FROM sedi WHERE flag_canc=0) AS sedi_es ON sedi_es.id_sede = indirizzi_succ.id_sede::integer
						LEFT JOIN (SELECT * FROM scuole WHERE flag_canc=0) AS scuole_es ON scuole_es.id_scuola = sedi_es.id_scuola::integer
					WHERE
						classi_succ.flag_canc=0
						AND
						indirizzi_succ.flag_canc=0
						AND
						id_codice_ministeriale <> 94
						AND
						tipo_indirizzo != 'CORSO'
					ORDER BY descrizione_indirizzi, classe";
/*
			$sql = "SELECT
			DISTINCT
				descrizione_indirizzi,
				classe,
				sezione,
				id_classe,
				id_indirizzo
			FROM classi_complete
			WHERE id_codice_ministeriale <> 94
				AND tipo_indirizzo != 'CORSO'
			ORDER BY descrizione_indirizzi, classe";
*/
		}

		$query = $this->user->data->db->prepare($sql);
		$query->execute();

		$rows = $query->fetchAll(PDO::FETCH_ASSOC);

		foreach ($rows as $key => $row) {
			if($parameter['tipo_raggruppamento'] != "ESPLODI_SEZIONI"){
				$chiave = $row['id_indirizzo'].'_'.$row['classe'];
				$results[$chiave]['id'] = $chiave;
				$results[$chiave]['id_classe'] = '';
				$results[$chiave]['id_indirizzo'] = $row['id_indirizzo'];
				$results[$chiave]['classe'] = $row['classe'];
				$results[$chiave]['sezione'] = '';
				$results[$chiave]['indirizzo'] = $row['descrizione_indirizzi'];
				if(intval($row['id_sede']) > 0){
					$results[$chiave]['abbinato_sede'] = 'SI';
				}else{
					$results[$chiave]['abbinato_sede'] = 'NO';
				}
				$results[$chiave]['tipo_raggruppamento'] = $parameter['tipo_raggruppamento'];
				$sql_mat = "SELECT
								id_materia,
								descrizione,
								in_media_pagelle,
								tipo_materia
							FROM
								materie
							WHERE id_materia IN
								(
									SELECT DISTINCT id_materia
									FROM classi_prof_materie_succ
									WHERE id_classe IN
									(
										SELECT
											classi_succ.id_classe
										FROM
											classi_succ
											INNER JOIN indirizzi_succ ON indirizzi_succ.id_indirizzo=classi_succ.id_indirizzo
											LEFT JOIN (SELECT * FROM sedi WHERE flag_canc=0) AS sedi_es ON sedi_es.id_sede = indirizzi_succ.id_sede::integer
											LEFT JOIN (SELECT * FROM scuole WHERE flag_canc=0) AS scuole_es ON scuole_es.id_scuola = sedi_es.id_scuola::integer
										WHERE
											classi_succ.flag_canc=0
											AND
											indirizzi_succ.flag_canc=0
											AND
											indirizzi_succ.id_indirizzo = " . $row['id_indirizzo'] . "
											AND
											classi_succ.classe = '" . $row['classe'] . "'
									)
									AND
									id_professore = " . $parameter['id_professore'] . "
								)
							AND
							tipo_materia <> 'CONDOTTA'" . $and_materia_in_media;

				file_put_contents('/tmp/sql_mat.txt', print_r($sql_mat,true));
				$query_mat = $this->user->data->db->prepare($sql_mat);
				$query_mat->execute();

				$rows_mat = $query_mat->fetchAll(PDO::FETCH_ASSOC);

				if(count($rows_mat) > 0){
					$results[$chiave]['almeno_uno_attivo'] = 'SI';
				} else {
					$results[$chiave]['almeno_uno_attivo'] = 'NO';
				}

				foreach ($rows_mat as $key_mat => $row_mat) {
					$results[$chiave]['materie'][$row_mat['id_materia']] = $row_mat;
					$results[$chiave]['materie'][$row_mat['id_materia']]['attiva_docente'] = 'SI';
				}
				$sql_mat = "SELECT
								id_materia,
								descrizione,
								in_media_pagelle,
								tipo_materia
							FROM
								materie
							WHERE id_materia IN
								(
									SELECT DISTINCT id_materia
									FROM classi_prof_materie_succ
									WHERE id_classe IN
									(
										SELECT
											classi_succ.id_classe
										FROM
											classi_succ
											INNER JOIN indirizzi_succ ON indirizzi_succ.id_indirizzo=classi_succ.id_indirizzo
											LEFT JOIN (SELECT * FROM sedi WHERE flag_canc=0) AS sedi_es ON sedi_es.id_sede = indirizzi_succ.id_sede::integer
											LEFT JOIN (SELECT * FROM scuole WHERE flag_canc=0) AS scuole_es ON scuole_es.id_scuola = sedi_es.id_scuola::integer
										WHERE
											classi_succ.flag_canc=0
											AND
											indirizzi_succ.flag_canc=0
											AND
											indirizzi_succ.id_indirizzo = " . $row['id_indirizzo'] . "
											AND
											classi_succ.classe = '" . $row['classe'] . "'
									)
									AND
									id_materia
									NOT IN
									(
										SELECT DISTINCT id_materia
										FROM classi_prof_materie_succ
										WHERE id_classe IN
										(
											SELECT
												classi_succ.id_classe
											FROM
												classi_succ
												INNER JOIN indirizzi_succ ON indirizzi_succ.id_indirizzo=classi_succ.id_indirizzo
												LEFT JOIN (SELECT * FROM sedi WHERE flag_canc=0) AS sedi_es ON sedi_es.id_sede = indirizzi_succ.id_sede::integer
												LEFT JOIN (SELECT * FROM scuole WHERE flag_canc=0) AS scuole_es ON scuole_es.id_scuola = sedi_es.id_scuola::integer
											WHERE
												classi_succ.flag_canc=0
												AND
												indirizzi_succ.flag_canc=0
												AND
												indirizzi_succ.id_indirizzo = " . $row['id_indirizzo'] . "
												AND
												classi_succ.classe = '" . $row['classe'] . "'
										)
										AND
										id_professore = " . $parameter['id_professore'] . "
									)
								)
							AND
							tipo_materia <> 'CONDOTTA'" . $and_materia_in_media;


				$query_mat = $this->user->data->db->prepare($sql_mat);
				$query_mat->execute();

				$rows_mat = $query_mat->fetchAll(PDO::FETCH_ASSOC);
				foreach ($rows_mat as $key_mat => $row_mat) {
					$results[$chiave]['materie'][$row_mat['id_materia']] = $row_mat;
					$results[$chiave]['materie'][$row_mat['id_materia']]['attiva_docente'] = 'NO';
				}
			} else {
				$chiave = $row['id_classe'].'_'.$row['classe'];
				$results[$chiave]['id'] = $chiave;
				$results[$chiave]['id_classe'] = $row['id_classe'];
				$results[$chiave]['id_indirizzo'] = $row['id_indirizzo'];
				$results[$chiave]['classe'] = $row['classe'];
				$results[$chiave]['sezione'] = $row['sezione'];
				$results[$chiave]['indirizzo'] = $row['descrizione_indirizzi'];
				$results[$chiave]['tipo_raggruppamento'] = $parameter['tipo_raggruppamento'];
				$sql_mat = "SELECT
								id_materia,
								descrizione,
								in_media_pagelle,
								tipo_materia
							FROM
								materie
							WHERE id_materia IN
								(
									SELECT DISTINCT id_materia
									FROM classi_prof_materie_succ
									WHERE id_classe  = " . $row['id_classe'] . "
									AND
									id_professore = " . $parameter['id_professore'] . "
								)
							AND
							tipo_materia <> 'CONDOTTA'" . $and_materia_in_media;
				$query_mat = $this->user->data->db->prepare($sql_mat);
				$query_mat->execute();

				$rows_mat = $query_mat->fetchAll(PDO::FETCH_ASSOC);

				if(count($rows_mat) > 0){
					$results[$chiave]['almeno_uno_attivo'] = 'SI';
				}else{
					$results[$chiave]['almeno_uno_attivo'] = 'NO';
				}

				foreach ($rows_mat as $key_mat => $row_mat) {
					$results[$chiave]['materie'][$row_mat['id_materia']] = $row_mat;
					$results[$chiave]['materie'][$row_mat['id_materia']]['attiva_docente'] = 'SI';
				}
				$sql_mat = "SELECT
								id_materia,
								descrizione,
								in_media_pagelle,
								tipo_materia
							FROM
								materie
							WHERE id_materia IN
								(
									SELECT DISTINCT id_materia
									FROM classi_prof_materie_succ
									WHERE id_classe = " . $row['id_classe'] . "
									AND
									id_materia
									NOT IN
									(
										SELECT DISTINCT id_materia
										FROM classi_prof_materie_succ
										WHERE id_classe = " . $row['id_classe'] . "
										AND
										id_professore = " . $parameter['id_professore'] . "
									)
								)
							AND
							tipo_materia <> 'CONDOTTA'" . $and_materia_in_media;
				$query_mat = $this->user->data->db->prepare($sql_mat);
				$query_mat->execute();

				$rows_mat = $query_mat->fetchAll(PDO::FETCH_ASSOC);
				foreach ($rows_mat as $key_mat => $row_mat) {
					$results[$chiave]['materie'][$row_mat['id_materia']] = $row_mat;
					$results[$chiave]['materie'][$row_mat['id_materia']]['attiva_docente'] = 'NO';
				}
			}
		}

		//estrazione adozioni presenti
		$and = "";
		if (isset($parameter['id_raggruppamento'])) {
			$and = " AND a.id_raggruppamento = '{$parameter['id_raggruppamento']}' ";
		}

		if (isset($parameter['id_raggruppamento']) || $parameter['includi_adozioni'] == 'SI'){
			$sql = "
				SELECT a.*,
					u.cognome||' '||u.nome as chi_inserisce_testo,
					u2.cognome||' '||u2.nome as chi_modifica_testo
				FROM adozioni_aie a
				LEFT JOIN utenti u ON a.chi_inserisce = u.id_utente
				LEFT JOIN utenti u2 ON a.chi_modifica = u2.id_utente
				WHERE a.tipo_raggruppamento = '{$parameter['tipo_raggruppamento']}'
					{$and}
					AND a.flag_canc = 0
			";

			$query = $this->user->data->db->prepare($sql);
			$query->execute();

			$rows = $query->fetchAll(PDO::FETCH_ASSOC);

			foreach ($rows as $row){
				$adozione = [];
				foreach ($row as $key => $value){
					if ($key == 'dati_json'){
						$adozione[$key] = json_decode($value);
					} else {
						$adozione[$key] = $this->user->data->DecodeField($value);
					}
				}

				if (isset($results[$adozione['id_raggruppamento']])){
					$results[$adozione['id_raggruppamento']]['materie'][$adozione['id_materia_mastercom']]['adozioni'][] = $adozione;
				}
			}
		}

		if(strlen($parameter['anno_db']) > 0){
			$previous_old = $this->user->data->SetDb($previous);
		}

		return $results;
		/*}}}*/
	}

	public function inserisciAdozione($dati)
	{

		if (strlen($dati['anno_db']) > 0) {
			$previous = $this->user->data->SetDb($dati['anno_db']);
		}

		$elenco_campi = $this->definisciCampiTabellaAdozioni();

		//settaggio current_user
		$errori = [];
		$current_user = $this->user->isUser(true);
		if(!($current_user > 0)){
			$current_user = $this->user->isTeacher(true);
			if(!($current_user > 0)){
				$errori['utente_non_valido'] = $this->user->getUserInfo();
			}
		}

		if ($dati['adozione']['tipo_inserimento'] == 'MATERIALE_DOCENTE'){
			// vuoto tutto il necessario
			$dati['adozione']['isbn'] = '';
			$dati['adozione']['codice_prodotto'] = 'DEFAULT';
			$dati['adozione']['autori'] = '';
			$dati['adozione']['editore_id'] = 'DEFAULT';
			$dati['adozione']['descrizione_editore'] = '';
			$dati['adozione']['titolo'] = '';
			$dati['adozione']['sottotitolo'] = '';
			$dati['adozione']['materia_id_aie'] = 'DEFAULT';
			$dati['adozione']['descrizione_materia_aie'] = '';
			$dati['adozione']['materia_alternativa_id_aie'] = 'DEFAULT';
			$dati['adozione']['descrizione_materia_alternativa_aie'] = '';
			$dati['adozione']['numero_volumi'] = 'DEFAULT';
			$dati['adozione']['progressivo_volume'] = 'DEFAULT';
			$dati['adozione']['anno_edizione'] = 'DEFAULT';
			$dati['adozione']['prezzo'] = 'DEFAULT';
			$dati['adozione']['modalita_tipo_id'] = 'DEFAULT';
			$dati['adozione']['tipo_scuola_id'] = '';
			$dati['adozione']['da_acquistare'] = 'NO';
			$dati['adozione']['consigliato'] = '';
			$dati['adozione']['nuova_adozione'] = '';
			$dati['adozione']['in_possesso'] = '';
			$dati['adozione']['anno_prima_adozione'] = 'DEFAULT';
			$dati['adozione']['tipo_libro'] = '';
			$dati['adozione']['sito_acquisto'] = '';
			$dati['adozione']['app_lettura'] = '';
			$dati['adozione']['disponibile_acquisto_sito'] = '';
			$dati['adozione']['dichiarazione_editore'] = '';
			$dati['adozione']['versione_accessibile'] = '';
			$dati['adozione']['tipo_versione_accessibile'] = '';
			$dati['adozione']['dati_json'] = '';
			$dati['adozione']['agente'] = '';
		}

		if (isset($dati['adozione']['id_classe']) && !($dati['adozione']['id_classe'] > 0)){
			// la classe potrebbe essere presente ma vuota
			$dati['adozione']['id_classe'] = 'DEFAULT';
		}

		if ($dati['adozione']['id_adozione_aie'] > 0){
			// MODIFICA
			$id_adozione_aie = $dati['adozione']['id_adozione_aie'];
			unset($dati['adozione']['id_adozione_aie']);
			$campi_da_modificare = [];
			foreach ($dati['adozione'] as $nome => $valore) {
				if (isset($elenco_campi[$nome])) {
					if ($elenco_campi[$nome]['tipo'] == 'string' && $nome != 'dati_json') {
						$valore = $this->user->data->EncodeField($valore);
					}
					if ($elenco_campi[$nome]['tipo'] != 'string' && (is_null($valore) || $valore == '')) {
						$valore = 'NULL';
					}

					$campi_da_modificare[] = ($elenco_campi[$nome]['tipo'] == 'string') ? $nome . "= '" . $valore . "'" : $nome . "= " . $valore;
				}
			}

			$sql = "UPDATE adozioni_aie SET " . implode(', ', $campi_da_modificare) . ' WHERE id_adozione_aie = ' . $id_adozione_aie;

			$query = $this->user->data->db->prepare($sql);
			$query->execute();

			$mat_oggetti = [
				"id_adozione_aie" => $id_adozione_aie
			];

			$this->user->data->inserisciLog($mat_oggetti, 'adozioni_aie', $current_user, "INTERFACCIA", "MODIFICA");

			if (strlen($dati['anno_db']) > 0) {
				$previous_old = $this->user->data->SetDb($previous);
			}

			return $id_adozione_aie;
		} else {
			// INSERIMENTO
			$campi_da_inserire = [];
			$stringa_valori = "";
			foreach ($dati['adozione'] as $nome => $valore){
				if (isset($elenco_campi[$nome])){
					if ($elenco_campi[$nome]['tipo'] == 'string' && $nome != 'dati_json'){
						$valore = $this->user->data->EncodeField($valore);
					}

					$campi_da_inserire['nomi'][] = $nome;
					$campi_da_inserire['valori'][] = $valore;
					if ($elenco_campi[$nome]['tipo'] != 'string' && is_null($valore)){
						$valore = 'NULL';
					}
					$stringa_valori .= ($elenco_campi[$nome]['tipo'] == 'string') ? "'".$valore."'," : $valore.",";
				}
			}
			$stringa_valori = substr($stringa_valori, 0, -1);

			$sql = "INSERT INTO adozioni_aie
				(" . implode(", ", $campi_da_inserire['nomi']) . ") VALUES (" . $stringa_valori . ") RETURNING id_adozione_aie";
Logging::debug($sql);
			$query = $this->user->data->db->prepare($sql);
			$query->execute();
			$id_adozione_aie = $query->fetchAll(PDO::FETCH_ASSOC)[0]['id_adozione_aie'];

			$mat_oggetti = [
				"id_adozione_aie" => $id_adozione_aie
			];

			$this->user->data->inserisciLog($mat_oggetti, 'adozioni_aie', $current_user, "INTERFACCIA", "INSERIMENTO");

			if (strlen($dati['anno_db']) > 0) {
				$previous_old = $this->user->data->SetDb($previous);
			}

			return $id_adozione_aie;
		}
	}

	 /**
	 * Funzione in PUT
	 * aggiorna il prezzo di tutte le adozioni verificandole sul db AIE
	 * i dati utilizzati sono:
	 * $dati['db_richiesto'] deve contenere il nome completo del database da utilizzare (opzionale, se non espresso si usa db attuale)
     * @param array $dati
	 */
	public function aggiornaPrezzoAdozione($dati)
	{
		$host_aie = 'https://serveraie-mastercom.registroelettronico.com';
        $parametri_login_aie = [
            'username'  =>  'utente_aie',
            'password'  =>  'fV$?2TbxZq'
        ];

		//login aie se necessario
		if (!isset($token_aie) || !strlen($token_aie) > 0) {
			$token_aie = $this->user->data->chiamataApi($host_aie . "/next-api/v1/login", $parametri_login_aie, 'POST');
		}

		$parametri_estrazione_libro = [];
		$dati_libri_json = $this->user->data->chiamataApi($host_aie . "/next-api/v1/adozioni_libri/liste/prezzi_opere", $parametri_estrazione_libro, 'GET', '', $token_aie);

		$dati_libri = json_decode($dati_libri_json, true);

		if (strlen($dati['db_richiesto']) > 0) {
			$previous = $this->user->data->SetDb($dati['db_richiesto']);
		}

		$sql = " SELECT *
					FROM adozioni_aie";

		$query = $this->user->data->db->prepare($sql);
		$query->execute();
		$adozioni_presenti = $query->fetchAll(PDO::FETCH_ASSOC);

		//settaggio current_user
		$errori = [];
		$current_user = $this->user->isUser(true);
		if(!($current_user > 0)){
			$current_user = $this->user->isTeacher(true);
			if(!($current_user > 0)){
				$errori['utente_non_valido'] = $this->user->getUserInfo();
			}
		}

		file_put_contents('/tmp/file_AIE_agg_prezzi.txt', print_r('',true));
		foreach ($adozioni_presenti as $singola_adozione) {
			$prezzo_estratto = floatval(substr($dati_libri[$singola_adozione["codice_prodotto"]]['prezzo'], 0,-2) . "." . substr($dati_libri[$singola_adozione["codice_prodotto"]]['prezzo'],-2));

			if (($singola_adozione['id_adozione_aie'] > 0) && ($prezzo_estratto != $singola_adozione['prezzo']) && (intval($dati_libri[$singola_adozione["codice_prodotto"]]['prezzo']) > 0)){
				$id_adozione_aie = $singola_adozione['id_adozione_aie'];

				$sql = "UPDATE
								adozioni_aie
							SET
								prezzo = $prezzo_estratto,
								dati_json = jsonb_set(dati_json::jsonb, '{prezzo}', '" . $dati_libri[$singola_adozione["codice_prodotto"]]['prezzo'] . "'::jsonb)::text
							WHERE
								id_adozione_aie = $id_adozione_aie";

				file_put_contents('/tmp/file_AIE_agg_prezzi.txt', print_r("\nprezzo estratto=" . $dati_libri[$singola_adozione["codice_prodotto"]]['prezzo'] . "\n",true), FILE_APPEND);
				file_put_contents('/tmp/file_AIE_agg_prezzi.txt', print_r("prezzo vecchio=" . $singola_adozione['prezzo'] . "\n",true), FILE_APPEND);
				file_put_contents('/tmp/file_AIE_agg_prezzi.txt', print_r($sql,true), FILE_APPEND);
				$query = $this->user->data->db->prepare($sql);
				$query->execute();

				$mat_oggetti = [
					"id_adozione_aie" => $id_adozione_aie
				];

				$this->user->data->inserisciLog($mat_oggetti, 'adozioni_aie', $current_user, "INTERFACCIA", "MODIFICA");
			}
		}

		if (strlen($dati['db_richiesto']) > 0) {
			$previous_old = $this->user->data->SetDb($previous);
		}

		return $id_adozione_aie;
	}

	public function softDelete($id_adozione, $input)
	{
		if ($id_adozione > 0){

			if (strlen($input['anno_db']) > 0) {
				$previous = $this->user->data->SetDb($input['anno_db']);
			}

			//settaggio current_user
			$errori = [];
			$current_user = $this->user->isUser(true);
			if(!($current_user > 0)){
				$current_user = $this->user->isTeacher(true);
				if(!($current_user > 0)){
					$errori['utente_non_valido'] = $this->user->getUserInfo();
				}
			}

			$delete = "UPDATE adozioni_aie
						SET flag_canc = ".time()."
						WHERE id_adozione_aie = ".$id_adozione;
			$query = $this->user->data->db->prepare($delete);
			$query->execute();

			$mat_oggetti = [
				"id_adozione_aie" => $id_adozione
			];

			$this->user->data->inserisciLog($mat_oggetti, 'adozioni_aie', $current_user, "INTERFACCIA", "ELIMINAZIONE");

			if (strlen($input['anno_db']) > 0) {
				$previous_old = $this->user->data->SetDb($previous);
			}

			return true;
		} else {
			return false;
		}
	}

	public function getAdozione($id_adozione_aie, $input)
	{
		if (strlen($input['anno_db']) > 0) {
			$previous = $this->user->data->SetDb($input['anno_db']);
		}

		$sql = "
				SELECT a.*,
					u.cognome||' '||u.nome as chi_inserisce_testo,
					u2.cognome||' '||u2.nome as chi_modifica_testo,
					m.descrizione as descrizione_materia_mastercom
				FROM adozioni_aie a
				LEFT JOIN utenti u ON a.chi_inserisce = u.id_utente
				LEFT JOIN utenti u2 ON a.chi_modifica = u2.id_utente
				LEFT JOIN materie m ON m.id_materia = a.id_materia_mastercom
				WHERE a.id_adozione_aie = {$id_adozione_aie}
					AND a.flag_canc = 0
			";

		$query = $this->user->data->db->prepare($sql);
		$query->execute();

		$rows = $query->fetchAll(PDO::FETCH_ASSOC);

		if (count($rows) > 0){
			$adozione = [];
			foreach ($rows[0] as $key => $value) {
				if ($key == 'dati_json') {
					$adozione[$key] = json_decode($value);
				} else {
					$adozione[$key] = $this->user->data->DecodeField($value);
				}
			}
		} else {
			$adozione = [];
		}

		if (strlen($input['anno_db']) > 0) {
			$previous_old = $this->user->data->SetDb($previous);
		}

		return $adozione;
	}

	public function updateStato($id_adozione_aie, $input)
	{
		if ($id_adozione_aie > 0){

			if (strlen($input['anno_db']) > 0) {
				$previous = $this->user->data->SetDb($input['anno_db']);
			}

			//settaggio current_user
			$errori = [];
			$current_user = $this->user->isUser(true);
			if(!($current_user > 0)){
				$current_user = $this->user->isTeacher(true);
				if(!($current_user > 0)){
					$errori['utente_non_valido'] = $this->user->getUserInfo();
				}
			}

			if (isset($input['stato'])){
				$nuovo_stato = strtoupper($input['stato']);
				$stato_manuale = true;
			} else {
				// calcolo automatico
				$a = $this->getAdozione($id_adozione_aie, $input);

				switch (true) {
					case ($a['approvazione_segreteria'] != $a['approvazione_responsabile_didattica']):
						$nuovo_stato = 'IN_REVISIONE';
						break;
					case ($a['approvazione_segreteria'] == 'SI' && $a['approvazione_responsabile_didattica'] == 'SI'):
						$nuovo_stato = 'CONFERMATA';
						break;
					case ($a['approvazione_segreteria'] == 'NO' && $a['approvazione_responsabile_didattica'] == 'NO'):
						$nuovo_stato = 'PROPOSTA';
						break;
					default:
						$nuovo_stato = 'ciuao';
						break;
				}
			}

			$update = "UPDATE adozioni_aie
						SET stato_adozione = '" . $nuovo_stato . "'
						WHERE id_adozione_aie = " . $id_adozione_aie;
			$query = $this->user->data->db->prepare($update);
			$query->execute();

			if ($stato_manuale){
				$mat_oggetti = [
					"id_adozione_aie" => $id_adozione_aie
				];

				$this->user->data->inserisciLog($mat_oggetti, 'adozioni_aie', $current_user, "INTERFACCIA", "MODIFICA");
			}

			if (strlen($input['anno_db']) > 0) {
				$previous_old = $this->user->data->SetDb($previous);
			}

			return $nuovo_stato;
		} else {
			return false;
		}
	}

	public function eliminaApprovazioni($input)
	{
		if (strlen($input['anno_db']) > 0) {
			$previous = $this->user->data->SetDb($input['anno_db']);
		}

		//settaggio current_user
		$errori = [];
		$current_user = $this->user->isUser(true);
		if(!($current_user > 0)){
			$current_user = $this->user->isTeacher(true);
			if(!($current_user > 0)){
				$errori['utente_non_valido'] = $this->user->getUserInfo();
			}
		}

		$update = "UPDATE adozioni_aie
					SET approvazione_segreteria = 'NO',
						approvazione_responsabile_didattica = 'NO',
						stato_adozione = 'PROPOSTA'
					";
		$query = $this->user->data->db->prepare($update);
		$query->execute();

		$mat_oggetti = [
			"approvazione_responsabile_didattica" => 'NO'
		];

		$this->user->data->inserisciLog($mat_oggetti, 'adozioni_aie', $current_user, "INTERFACCIA", "MODIFICA");

		$this->user->inserisciLogStorico("ELIMINAZIONE_APPROVAZIONI_ADOZIONI", "Eliminazione massiva approvazioni adozioni libri");

		if (strlen($input['anno_db']) > 0) {
			$previous_old = $this->user->data->SetDb($previous);
		}

		return true;
	}

	public function eliminaAdozioni($input)
	{
		if (strlen($input['anno_db']) > 0) {
			$previous = $this->user->data->SetDb($input['anno_db']);
		}

		//settaggio current_user
		$errori = [];
		$current_user = $this->user->isUser(true);
		if(!($current_user > 0)){
			$current_user = $this->user->isTeacher(true);
			if(!($current_user > 0)){
				$errori['utente_non_valido'] = $this->user->getUserInfo();
			}
		}
		$flag_canc = time();

		$update = "UPDATE adozioni_aie
					SET flag_canc = " . $flag_canc . "
					WHERE flag_canc = 0
					";
		$query = $this->user->data->db->prepare($update);
		$query->execute();

		$mat_oggetti = [
			"flag_canc" => $flag_canc
		];

		$this->user->data->inserisciLog($mat_oggetti, 'adozioni_aie', $current_user, "INTERFACCIA", "ELIMINAZIONE");

		$this->user->inserisciLogStorico("ELIMINAZIONE_ADOZIONI_TOTALI", "Eliminazione massiva adozioni libri");

		if (strlen($input['anno_db']) > 0) {
			$previous_old = $this->user->data->SetDb($previous);
		}

		return true;
	}

	private function definisciCampiTabellaAdozioni()
	{
		$campi = [
			'id_raggruppamento'	=>	[
				'nome'	=>	'id_raggruppamento',
				'tipo'	=>	'string'
			],
			'tipo_raggruppamento'	=>	[
				'nome'	=>	'tipo_raggruppamento',
				'tipo'	=>	'string'
			],
			'id_classe'	=>	[
				'nome'	=>	'id_classe',
				'tipo'	=>	'integer'
			],
			'id_indirizzo'	=>	[
				'nome'	=>	'id_indirizzo',
				'tipo'	=>	'integer'
			],
			'id_materia_mastercom'	=>	[
				'nome'	=>	'id_materia_mastercom',
				'tipo'	=>	'integer'
			],
			'isbn'	=>	[
				'nome'	=>	'isbn',
				'tipo'	=>	'string'
			],
			'codice_prodotto'	=>	[
				'nome'	=>	'codice_prodotto',
				'tipo'	=>	'integer'
			],
			'autori'	=>	[
				'nome'	=>	'autori',
				'tipo'	=>	'string'
			],
			'editore_id'	=>	[
				'nome'	=>	'editore_id',
				'tipo'	=>	'integer'
			],
			'descrizione_editore'	=>	[
				'nome'	=>	'descrizione_editore',
				'tipo'	=>	'string'
			],
			'titolo'	=>	[
				'nome'	=>	'titolo',
				'tipo'	=>	'string'
			],
			'sottotitolo'	=>	[
				'nome'	=>	'sottotitolo',
				'tipo'	=>	'string'
			],
			'materia_id_aie'	=>	[
				'nome'	=>	'materia_id_aie',
				'tipo'	=>	'integer'
			],
			'descrizione_materia_aie'	=>	[
				'nome'	=>	'descrizione_materia_aie',
				'tipo'	=>	'string'
			],
			'materia_alternativa_id_aie'	=>	[
				'nome'	=>	'materia_alternativa_id_aie',
				'tipo'	=>	'integer'
			],
			'descrizione_materia_alternativa_aie'	=>	[
				'nome'	=>	'descrizione_materia_alternativa_aie',
				'tipo'	=>	'string'
			],
			'numero_volumi'	=>	[
				'nome'	=>	'numero_volumi',
				'tipo'	=>	'integer'
			],
			'progressivo_volume'	=>	[
				'nome'	=>	'progressivo_volume',
				'tipo'	=>	'integer'
			],
			'anno_edizione'	=>	[
				'nome'	=>	'anno_edizione',
				'tipo'	=>	'integer'
			],
			'prezzo'	=>	[
				'nome'	=>	'prezzo',
				'tipo'	=>	'real'
			],
			'modalita_tipo_id'	=>	[
				'nome'	=>	'modalita_tipo_id',
				'tipo'	=>	'integer'
			],
			'tipo_scuola_id'	=>	[
				'nome'	=>	'tipo_scuola_id',
				'tipo'	=>	'string'
			],
			'da_acquistare'	=>	[
				'nome'	=>	'da_acquistare',
				'tipo'	=>	'string'
			],
			'consigliato'	=>	[
				'nome'	=>	'consigliato',
				'tipo'	=>	'string'
			],
			'nuova_adozione'	=>	[
				'nome'	=>	'nuova_adozione',
				'tipo'	=>	'string'
			],
			'in_possesso'	=>	[
				'nome'	=>	'in_possesso',
				'tipo'	=>	'string'
			],
			'anno_prima_adozione'	=>	[
				'nome'	=>	'anno_prima_adozione',
				'tipo'	=>	'integer'
			],
			'tipo_libro'	=>	[
				'nome'	=>	'tipo_libro',
				'tipo'	=>	'string'
			],
			'sito_acquisto'	=>	[
				'nome'	=>	'sito_acquisto',
				'tipo'	=>	'string'
			],
			'app_lettura'	=>	[
				'nome'	=>	'app_lettura',
				'tipo'	=>	'string'
			],
			'disponibile_acquisto_sito'	=>	[
				'nome'	=>	'disponibile_acquisto_sito',
				'tipo'	=>	'string'
			],
			'dichiarazione_editore'	=>	[
				'nome'	=>	'dichiarazione_editore',
				'tipo'	=>	'string'
			],
			'versione_accessibile'	=>	[
				'nome'	=>	'versione_accessibile',
				'tipo'	=>	'string'
			],
			'tipo_versione_accessibile'	=>	[
				'nome'	=>	'tipo_versione_accessibile',
				'tipo'	=>	'string'
			],
			'agente'	=>	[
				'nome'	=>	'agente',
				'tipo'	=>	'string'
			],
			'tipo_adozione'	=>	[
				'nome'	=>	'tipo_adozione',
				'tipo'	=>	'string'
			],
			'stato_adozione'	=>	[
				'nome'	=>	'stato_adozione',
				'tipo'	=>	'string'
			],
			'approvazione_segreteria'	=>	[
				'nome'	=>	'approvazione_segreteria',
				'tipo'	=>	'string'
			],
			'approvazione_responsabile_didattica'	=>	[
				'nome'	=>	'approvazione_responsabile_didattica',
				'tipo'	=>	'string'
			],
			'dati_json'	=>	[
				'nome'	=>	'dati_json',
				'tipo'	=>	'string'
			],
			'adozione_extra'	=>	[
				'nome'	=>	'adozione_extra',
				'tipo'	=>	'string'
			]
		];

		return $campi;
	}


	/**
	 * genera il file sulla base del tipo di trasforamzione scelta e sui dati che vengono passati
     * @param array $data
	 * Il campo $data['dati'] deve contenere i dati recuperati dalla funzione da lanciare sul server AIE getInfoClassiSediAIE()
 	 * Il campo $data['anno_db'] che contiene il db da utilizzare
	 *
	 * 	 */
	public function getFileFormattedAIE($data = null) {
		/*{{{ */

		if(strlen($data['anno_db']) > 0){
			$previous = $this->user->data->SetDb($data['anno_db']);
		}

		$numero_cod_scuola_file = 0;
		$array_codici = [];
		$array_classi = [];
		$array_classi_studenti = [];

		$sql_classi_studenti = "	SELECT classi_succ.id_classe, classi_succ.num_stud_previsti_aie as num_stud
										FROM
											classi_succ
											INNER JOIN indirizzi_succ ON indirizzi_succ.id_indirizzo=classi_succ.id_indirizzo
											LEFT JOIN (SELECT * FROM sedi WHERE flag_canc=0) AS sedi_es ON sedi_es.id_sede = indirizzi_succ.id_sede::integer
											LEFT JOIN (SELECT * FROM scuole WHERE flag_canc=0) AS scuole_es ON scuole_es.id_scuola = sedi_es.id_scuola::integer
										WHERE
											classi_succ.flag_canc=0
											AND
											indirizzi_succ.flag_canc=0";



/*		$sql_classi_studenti = "	SELECT c.id_Classe, count(classi_studenti.id_studente) as num_stud
									from classi_complete c, classi_studenti, studenti s
									where 	c.id_classe=classi_studenti.id_classe
											and classi_studenti.flag_canc=0
											and s.id_studente=classi_studenti.id_studente
											and s.flag_canc=0
									group by c.id_classe";
*/
		$query_classi_studenti = $this->user->data->db->prepare($sql_classi_studenti);
		$query_classi_studenti->execute();

		$rows_classi_studenti = $query_classi_studenti->fetchAll(PDO::FETCH_ASSOC);

		$array_classi_studenti = [];
		foreach ($rows_classi_studenti as $row_classe_stud){
			$array_classi_studenti[$row_classe_stud['id_classe']] = $row_classe_stud['num_stud'];
		}
		file_put_contents('/tmp/array_classi_studenti.txt', print_r($array_classi_studenti,true));
		file_put_contents('/tmp/data.txt', print_r($data,true));

		$sql_ind_min = " SELECT
							id_indirizzo,
							codice,
							descrizione,
							classificazione
						FROM
							indirizzi_ministeriali
						WHERE
							flag_canc=0
							";
		$query_ind_min = $this->user->data->db->prepare($sql_ind_min);
		$query_ind_min->execute();

		$rows_ind_min = $query_ind_min->fetchAll(PDO::FETCH_ASSOC);

		$elenco_ind_min = [];
		foreach ($rows_ind_min as $row_ind_min){
			$elenco_ind_min[$row_ind_min['id_indirizzo']]['codice'] = $row_ind_min['codice'];
			$elenco_ind_min[$row_ind_min['id_indirizzo']]['classificazione'] = $row_ind_min['classificazione'];
		}
		file_put_contents('/tmp/elenco_ind_min.txt', print_r($elenco_ind_min,true));

		// prendo solo le principali quando c'e' il raggruppamento per anno
		$parametro_raggruppa_classi_adozioni = $this->user->data->getParametroMastercom('RAGGRUPPA_CLASSI_ADOZIONI');
		$and_ordinamento = ($parametro_raggruppa_classi_adozioni == 'SI') ? " AND classi_succ.ordinamento = '0' " : "";

		$sql_classi = " SELECT
							classi_succ.id_classe,
							classi_succ.sezione,
							classi_succ.classe,
							classi_succ.id_indirizzo,
							classi_succ.ordinamento,
							classi_succ.num_stud_previsti_aie,
							indirizzi_succ.codice AS codice_indirizzi,
							indirizzi_succ.descrizione AS descrizione_indirizzi,
							indirizzi_succ.tipo_indirizzo,
							indirizzi_succ.id_sede AS id_sede_indirizzi,
							indirizzi_succ.id_codice_ministeriale,
							indirizzi_succ.serale,
							scuole_es.id_scuola,
							scuole_es.descrizione AS descrizione_scuola,
							scuole_es.codice_meccanografico,
							scuole_es.sito_scuola,
							scuole_es.codice_meccanografico_secondario
						FROM
							classi_succ
							INNER JOIN indirizzi_succ ON indirizzi_succ.id_indirizzo=classi_succ.id_indirizzo
							LEFT JOIN (SELECT * FROM sedi WHERE flag_canc=0) AS sedi_es ON sedi_es.id_sede = indirizzi_succ.id_sede::integer
							LEFT JOIN (SELECT * FROM scuole WHERE flag_canc=0) AS scuole_es ON scuole_es.id_scuola = sedi_es.id_scuola::integer
						WHERE
							classi_succ.flag_canc=0
							AND
							indirizzi_succ.flag_canc=0
							AND
							indirizzi_succ.tipo_indirizzo != 'CORSO'
							AND
							indirizzi_succ.id_codice_ministeriale != 94 --fake
							" . $and_ordinamento;

		//$sql_classi = " SELECT c.*
		//				FROM classi_complete c";

		$query_classi = $this->user->data->db->prepare($sql_classi);
		$query_classi->execute();

		$rows_classi = $query_classi->fetchAll(PDO::FETCH_ASSOC);

		foreach ($rows_classi as $row_classe){
			if (strpos(strtolower($row_classe['descrizione_indirizzi']), 'serale') === false && $row_classe['serale'] == 0){
				$chiave_indirizzo = $row_classe['id_indirizzo'] . '_' . $row_classe['classe'];
				$row_classe['COD_MIN_IND'] = $elenco_ind_min[$row_classe['id_codice_ministeriale']]['codice'];
				$row_classe['classificazione'] = $elenco_ind_min[$row_classe['id_codice_ministeriale']]['classificazione'];
				$array_classi['indirizzi'][$chiave_indirizzo][$row_classe['id_classe']] = $row_classe;
				$array_classi['indirizzi'][$chiave_indirizzo][$row_classe['id_classe']]['ALUNNI'] = $row_classe['num_stud_previsti_aie'];
				$array_classi['classi'][$row_classe['id_classe']] = $row_classe;
				$array_classi['classi'][$row_classe['id_classe']]['ALUNNI'] = $row_classe['num_stud_previsti_aie'];
			} else {
				file_put_contents('/tmp/serale.txt', print_r($row_classe,true));
			}
		}

		file_put_contents('/tmp/array_classi.txt', print_r($array_classi,true));
		$sql = " SELECT a.*
					FROM adozioni_aie a
					WHERE a.flag_canc = 0";

		$query = $this->user->data->db->prepare($sql);
		$query->execute();

		$rows = $query->fetchAll(PDO::FETCH_ASSOC);
		$adozioni_totali = [];
		file_put_contents('/tmp/rows.txt', print_r($rows,true));

		// estraggo le mail delle sedi per popolarle in caso non siano presenti su aie
		$email_sedi = [];
		$sql = "SELECT DISTINCT id_indirizzo, email1_sedi, telefono_sedi FROM classi_complete";
		$query = $this->user->data->db->prepare($sql);
		$query->execute();
		$rows_email_sedi = $query->fetchAll(PDO::FETCH_ASSOC);
		foreach ($rows_email_sedi as $row_email_sedi){
			$email_sedi[$row_email_sedi['id_indirizzo']] = $row_email_sedi['email1_sedi'];
			$telefono_sedi[$row_email_sedi['id_indirizzo']] = $row_email_sedi['telefono_sedi'];
		}
								
		file_put_contents('/tmp/telefono_sedi.txt', print_r($telefono_sedi,true), FILE_APPEND);

		$codici_da_vuotare = [
			"ITAF",
			"ITCA",
			"IT24",
			"IT01",
			'LIIS'
		];

		foreach ($rows as $row){
			if ((isset($array_classi['classi'][$row['id_classe']]) && $row['tipo_raggruppamento'] == 'ESPLODI_SEZIONI')
				 || (isset($array_classi['indirizzi'][$row['id_raggruppamento']]) && $row['tipo_raggruppamento'] != 'ESPLODI_SEZIONI')
				){
				$adozione = [];
				foreach ($row as $key => $value){
					if ($key == 'dati_json'){
						$adozione[$key] = json_decode($value, true);
					} else {
						$adozione[$key] = $this->user->data->DecodeField($value);
						$value = $this->user->data->DecodeField($value);
					}

					switch ($key) {
						case 'consigliato':
							$adozione['CONSIG'] = $value;
							//$adozione['CONSIG2'] = $value;
							//$adozione['CONSIG'] = '';
						break;
						case 'da_acquistare':
							if(strtoupper($value) == 'SI'){
								$adozione['INUSO'] = 'N';
							}else{
								$adozione['INUSO'] = 'S';
							}
						break;
						case 'in_possesso':
							$adozione['INPOSS'] = $value;
						break;
						case 'codice_prodotto':
							$adozione['CODPRO'] = $value;
						break;
						case 'isbn':
							$adozione['ISBN13'] = $value;
						break;
						case 'autori':
							$adozione['AUTORE'] = $value;
						break;
						case 'editore_id':
							$adozione['CODEDI'] = $value;
						break;
						case 'titolo':
							$adozione['TITOLO'] = $value;
						break;
						case 'progressivo_volume':
							$adozione['VOLUME'] = $value;
						break;
						case 'materia_id_aie':
							$adozione['CODMAT'] = $value;
						break;
						case 'approvazione_segreteria':
							$adozione['APPROVAZIONE_SEGRETERIA'] = $value;
						break;
						case 'approvazione_responsabile_didattica':
							$adozione['APPROVAZIONE_RESPONSABILE'] = $value;
						break;
						case 'anno_prima_adozione':
							$adozione['ANNOADOZIONE'] = intval($value);
						break;

					}
				}

				if(count($adozione) > 0){
					$adozioni_totali[] = $adozione;
					if($row['tipo_raggruppamento'] == 'ESPLODI_SEZIONI'){
						$test_classe = 'NO';
						foreach($array_classi_studenti as $id_classe_test => $valore_test){
							if($id_classe_test == $row['id_classe']){
								$test_classe = 'SI';
							}
						}
						if($test_classe == 'SI'){
							$cod_min_ind = $array_classi['classi'][$row['id_classe']]['COD_MIN_IND'];
							$id_classe_row = $row['id_classe'];
							$anno_classe = $array_classi['classi'][$row['id_classe']]['classe'];
							$sezione_classe = $array_classi['classi'][$row['id_classe']]['sezione'];
							$codice_mecc_riferimento = $array_classi['classi'][$row['id_classe']]['codice_meccanografico_secondario'];
							$record_mecc = $data['dati'][$codice_mecc_riferimento][$anno_classe][$sezione_classe];

							$usa_classid = 'SI';
	/*						if(count($record_mecc) == 0){
								$record_mecc = $data['dati'][$codice_mecc_riferimento][$anno_classe][array_keys($data['dati'][$codice_mecc_riferimento][$anno_classe])[0]];
								$usa_classid = 'NO';
							}*/

							if(count($record_mecc) == 0){
								file_put_contents('/tmp/riga_rotta.txt', print_r($row['id_raggruppamento'],true), FILE_APPEND);
								file_put_contents('/tmp/riga_rotta.txt', print_r(chr(10),true), FILE_APPEND);
								foreach($data['dati'][$codice_mecc_riferimento][$anno_classe] as $classe_test){
									if($classe_test['CODSPR'] == $cod_min_ind){
										$record_mecc = $classe_test;
										file_put_contents('/tmp/riga_rotta.txt', print_r("\n1681 classe_test",true), FILE_APPEND);
									}
								}
								if(count($record_mecc) == 0){
									$record_mecc = $data['dati'][$codice_mecc_riferimento][$anno_classe][array_keys($data['dati'][$codice_mecc_riferimento][$anno_classe])[0]];
									file_put_contents('/tmp/riga_rotta.txt', print_r("\n1686 record_mecc",true), FILE_APPEND);
									if(count($record_mecc) == 0){
										file_put_contents('/tmp/riga_rotta.txt', print_r("\n1688 record_mecc",true), FILE_APPEND);
										/*foreach($data['dati'][$codice_mecc_riferimento] as $singolo_anno_test){
											foreach($singolo_anno_test as $singola_classe_test){
												$record_mecc = $singola_classe_test;
												break 2;
											}
										}*/
										$record_mecc = $data['dati'][$codice_mecc_riferimento][0][0];
										/*
										$record_mecc['CODSPR'] = $cod_min_ind;
										$record_mecc['CODSPC'] = '';
										if($array_classi['classi'][$row['id_classe']]['classificazione'] == 'MM'){
											$record_mecc['TIPSCU'] = 'MM';
										}elseif($array_classi['classi'][$row['id_classe']]['classificazione'] == 'EE'){
											$record_mecc['TIPSCU'] = 'EE';
										}else{
											if($anno_classe > 2){
												$record_mecc['TIPSCU'] = 'NT';
											}else{
												$record_mecc['TIPSCU'] = 'NO';
											}
										}*/
									}

									if ($record_mecc['COSEDE'] == 0) {
										$record_mecc['COSEDE'] = $record_mecc['COSCUO'];
									}
								}
								$usa_classid = 'NO';
							}

							$record_mecc['CODSPR'] = $cod_min_ind;
							//$record_mecc['CODSPC'] = '';
							file_put_contents('/tmp/riga_rotta.txt', print_r(chr(10),true), FILE_APPEND);
							file_put_contents('/tmp/riga_rotta.txt', print_r($record_mecc,true), FILE_APPEND);
							file_put_contents('/tmp/riga_rotta.txt', print_r(chr(10),true), FILE_APPEND);

							//modifica fatta per tradurre i nostri codici nei codici di AIE
							if ($cod_min_ind == 'EA01') {
								$record_mecc['CODSPC'] = '';
							}
							if($cod_min_ind == 'EA02'){
								$record_mecc['CODSPC'] = 'ESABAC';
								$record_mecc['CODSPR'] = 'LI02';
							}
							if($cod_min_ind == 'EA03'){
								$record_mecc['CODSPC'] = 'ESABAC';
								$record_mecc['CODSPR'] = 'EA03';
							}
							if ($cod_min_ind == 'EA04') {
								$record_mecc['CODSPC'] = '';
							}
							if($cod_min_ind == '0924'){
								$record_mecc['CODSPC'] = '';
							}
							if($cod_min_ind == 'LI01'){
								$record_mecc['CODSPC'] = '';
							}
							if($cod_min_ind == 'LI02'){
								$record_mecc['CODSPC'] = '';
							}
							if($cod_min_ind == 'LI03'){
								$record_mecc['CODSPC'] = '';
							}
							if($cod_min_ind == 'LI11'){
								$record_mecc['CODSPC'] = '';
							}
							if($cod_min_ind == 'LI12'){
								$record_mecc['CODSPC'] = '';
							}
							if($cod_min_ind == 'LI13'){
								$record_mecc['CODSPC'] = '';
							}
							if($cod_min_ind == 'LI15'){
								$record_mecc['CODSPC'] = '';
							}
							if($cod_min_ind == 'LIB6'){
								$record_mecc['CODSPC'] = '';
							}
							if($cod_min_ind == 'Q315'){
								$record_mecc['CODSPC'] = '';
							}
							if ($cod_min_ind == 'ITIA') {
								$record_mecc['CODSPC'] = '';
							}
							if ($cod_min_ind == 'IT13') {
								$record_mecc['CODSPC'] = '';
							}
							if ($cod_min_ind == 'ITEN') {
								$record_mecc['CODSPC'] = '';
							}
							if ($cod_min_ind == 'ITAT') {
								$record_mecc['CODSPC'] = '';
							}
							if ($cod_min_ind == 'ITMM') {
								$record_mecc['CODSPC'] = '';
							}
							if ($cod_min_ind == 'IT05') {
								$record_mecc['CODSPC'] = '';
							}
							if ($cod_min_ind == 'IT13') {
								$record_mecc['CODSPC'] = '';
							}
							if ($cod_min_ind == '0515') {
								$record_mecc['CODSPC'] = '';
							}
							if ($cod_min_ind == 'EA06') {
								$record_mecc['CODSPC'] = '';
								$record_mecc['CODSPR'] = 'IT01';
							}
							if ($cod_min_ind == 'Q362') {
								$record_mecc['CODSPC'] = '';
								$record_mecc['CODSPR'] = 'IPQP';
							}
							if($cod_min_ind == 'IT08'){
								$record_mecc['CODSPC'] = '';
								$record_mecc['CODSPR'] = 'ITET';
							}
							if (in_array($cod_min_ind, $codici_da_vuotare)){
								$record_mecc['CODSPC'] = '';
							}

							if ($cod_min_ind == 'Q341') {
								$record_mecc['CODSPC'] = '';
								$record_mecc['CODSPR'] = 'IPQM';
							}

							if ($cod_min_ind == 'IP16') {
								$record_mecc['CODSPC'] = 'INGSPA';
								// $record_mecc['CODSPR'] = 'IPQM';
							}

							if ($cod_min_ind == 'Q305' && in_array($array_classi['classi'][$row['id_classe']]['classe'], ['1', '2'])) {
								$record_mecc['CODSPR'] = '0924';
							}

							if ($cod_min_ind == 'Q320' && in_array($array_classi['classi'][$row['id_classe']]['classe'], ['1', '2'])) {
								$record_mecc['CODSPC'] = 'OPRIPI';
								$record_mecc['CODSPR'] = 'IPQ3';
							}

							if($array_classi['classi'][$row['id_classe']]['classificazione'] == 'MM'){
								$record_mecc['TIPSCU'] = 'MM';
								if($record_mecc['COSCUO'] == 'VI1M008007'){
									$record_mecc['CODSPR'] = 'ORDBIL';
								} elseif ($record_mecc['CODSPC'] == 'INGFRA' || $record_mecc['CODSPC'] == 'INGTED'){
									$record_mecc['CODSPR'] = '30';
								} elseif ($record_mecc['CODSPC'] == 'TEDESC') {
									$record_mecc['CODSPR'] = 'MUSICA';
								} elseif ($record_mecc['CODSPC'] == 'OOINGL') {
									$record_mecc['CODSPR'] = '30ORE';
								}else{
									$record_mecc['CODSPR'] = '';
								}
							}elseif($array_classi['classi'][$row['id_classe']]['classificazione'] == 'EE'){
								$record_mecc['TIPSCU'] = 'EE';
								$record_mecc['CODSPR'] = '';
							}else{
								if($anno_classe > 2){
									$record_mecc['TIPSCU'] = 'NT';
								}else{
									$record_mecc['TIPSCU'] = 'NO';
								}
							}

							if ($record_mecc['CODSPC'] == 'ITDT') {
								$record_mecc['CODSPR'] = 'ITAM';
								$record_mecc['CODSPC'] = '';
							}

							if ($record_mecc['CODSPR'] == 'ITDT') {
								$record_mecc['CODSPR'] = 'ITAM';
								$record_mecc['CODSPC'] = '';
							}

							if ($record_mecc['CODSPC'] == 'LIB6') {
								$record_mecc['CODSPR'] = '';
							}

							if ($record_mecc['CODSPC'] == '30SETT') {
								$record_mecc['CODSPR'] = 'TEMLUN';
							}

							$usa_classid = 'NO';

							$elenco_classi[$row['id_classe']]['adozioni'][] = $adozione;
							$elenco_classi[$row['id_classe']]['SEZIONE'] = $array_classi['classi'][$row['id_classe']]['sezione'];
							$elenco_classi[$row['id_classe']]['CLASSE']  = $array_classi['classi'][$row['id_classe']]['classe'];
							$elenco_classi[$row['id_classe']]['ALUNNI']  = $array_classi['classi'][$row['id_classe']]['ALUNNI'];
							//$elenco_classi[$row['id_classe']]['ALUNNI']  = $array_classi_studenti[$row['id_classe']];
							$elenco_classi[$row['id_classe']]['TIPSCU']  = $record_mecc['TIPSCU'];
							$elenco_classi[$row['id_classe']]['CODSPC']  = $record_mecc['CODSPC'];
							$elenco_classi[$row['id_classe']]['CODSPR']  = $record_mecc['CODSPR'];

							if($usa_classid == 'SI'){
								$elenco_classi[$row['id_classe']]['CLID']  = $record_mecc['CLID'];
							}else{
								$elenco_classi[$row['id_classe']]['CLID']  = '';
							}

							if ($record_mecc['EMAIL'] == ''){
								$record_mecc['EMAIL'] = $email_sedi[$row['id_indirizzo']];
							}
									
							file_put_contents('/tmp/telefono_sedi.txt', print_r('--#' . $record_mecc['TELEFO'] . '#--'. "\n",true), FILE_APPEND);

							if ($record_mecc['TELEFO'] == ''){
								$record_mecc['TELEFO'] = $telefono_sedi[$row['id_indirizzo']];
							}

							$elenco_classi[$row['id_classe']]['CLID']  = $record_mecc['CLID'];

							$key_coscuo = $array_classi['classi'][$row['id_classe']]['codice_meccanografico_secondario'];
							$elenco_classi[$row['id_classe']]['COSCUO']  = $key_coscuo;

							if (isset($record_mecc['COSCUO']) && $record_mecc['COSCUO'] != '') { //in alcuni casi i dati arrivano mancanti o vuoti
								$array_codici[$key_coscuo]['COSCUO']  = $record_mecc['COSCUO'];
								$array_codici[$key_coscuo]['NOMSCU']  = $record_mecc['NOMSCU'];
								$array_codici[$key_coscuo]['INDSCU']  = $record_mecc['INDSCU'];
								$array_codici[$key_coscuo]['LOCSCU']  = $record_mecc['LOCSCU'];
								$array_codici[$key_coscuo]['CAPSCU']  = $record_mecc['CAPSCU'];
								$array_codici[$key_coscuo]['TELEFO']  = $record_mecc['TELEFO'];
								$array_codici[$key_coscuo]['FAX']     = $record_mecc['FAX'];
								$array_codici[$key_coscuo]['COSEDE']  = $record_mecc['COSEDE'];
								$array_codici[$key_coscuo]['EMAIL']   = $record_mecc['EMAIL'];
							}
						}

					} else {
						file_put_contents('/tmp/riga_rotta2.txt', print_r('',true));

						foreach ($array_classi['indirizzi'][$row['id_raggruppamento']] as $classe) {
							$cod_min_ind = $array_classi['classi'][$classe['id_classe']]['COD_MIN_IND'];
							$anno_classe = $array_classi['classi'][$classe['id_classe']]['classe'];
							$sezione_classe = $array_classi['classi'][$classe['id_classe']]['sezione'];
							$codice_mecc_riferimento = $array_classi['classi'][$classe['id_classe']]['codice_meccanografico_secondario'];
							$record_mecc = $data['dati'][$codice_mecc_riferimento][$anno_classe][$sezione_classe];
							$usa_classid = 'SI';

							if(count($record_mecc) == 0){
								file_put_contents('/tmp/riga_rotta2.txt', print_r($row['id_raggruppamento'],true), FILE_APPEND);
								file_put_contents('/tmp/riga_rotta2.txt', print_r(chr(10),true), FILE_APPEND);
								foreach($data['dati'][$codice_mecc_riferimento][$anno_classe] as $classe_test){
									if($classe_test['CODSPR'] == $cod_min_ind){
										$record_mecc = $classe_test;
									}
								}
								if(count($record_mecc) == 0){
									$record_mecc = $data['dati'][$codice_mecc_riferimento][$anno_classe][array_keys($data['dati'][$codice_mecc_riferimento][$anno_classe])[0]];
									/*	$record_mecc['CODSPC'] = '';
									$record_mecc['CODSPR'] = $cod_min_ind;
									if($array_classi['classi'][$classe['id_classe']]['classificazione'] == 'MM'){
										$record_mecc['TIPSCU'] = 'MM';
									}elseif($array_classi['classi'][$classe['id_classe']]['classificazione'] == 'EE'){
										$record_mecc['TIPSCU'] = 'EE';
									}else{
										if($anno_classe > 2){
											$record_mecc['TIPSCU'] = 'NT';
										}else{
											$record_mecc['TIPSCU'] = 'NO';
										}
									}*/

									if (count($record_mecc) == 0) {
										$record_mecc = $data['dati'][$codice_mecc_riferimento][0][0];
									}

									if ($record_mecc['COSEDE'] == 0){
										$record_mecc['COSEDE'] = $record_mecc['COSCUO'];
									}
								}
								$usa_classid = 'NO';
							}
							//$record_mecc['CODSPC'] = '';
							$record_mecc['CODSPR'] = $cod_min_ind;

							//modifica fatta per tradurre i nostri codici nei codici di AIE
							if($cod_min_ind == 'EA01'){
								$record_mecc['CODSPC'] = '';
							}
							if($cod_min_ind == 'EA02'){
								$record_mecc['CODSPC'] = 'ESABAC';
								$record_mecc['CODSPR'] = 'LI02';
							}
							if($cod_min_ind == 'EA03'){
								$record_mecc['CODSPC'] = 'ESABAC';
								$record_mecc['CODSPR'] = 'EA03';
							}
							if($cod_min_ind == '0924'){
								$record_mecc['CODSPC'] = '';
							}
							if($cod_min_ind == 'LI01'){
								$record_mecc['CODSPC'] = '';
							}
							if($cod_min_ind == 'LI02'){
								$record_mecc['CODSPC'] = '';
							}
							if($cod_min_ind == 'LI03'){
								$record_mecc['CODSPC'] = '';
							}
							if($cod_min_ind == 'LI11'){
								$record_mecc['CODSPC'] = '';
							}
							if($cod_min_ind == 'LI12'){
								$record_mecc['CODSPC'] = '';
							}
							if($cod_min_ind == 'LI13'){
								$record_mecc['CODSPC'] = '';
							}
							if($cod_min_ind == 'LI15'){
								$record_mecc['CODSPC'] = '';
							}
							if ($cod_min_ind == 'LIB6') {
								$record_mecc['CODSPC'] = '';
							}
							if($cod_min_ind == 'Q315'){
								$record_mecc['CODSPC'] = '';
							}
							if ($cod_min_ind == 'ITIA') {
								$record_mecc['CODSPC'] = '';
							}
							if ($cod_min_ind == 'IT13') {
								$record_mecc['CODSPC'] = '';
							}
							if ($cod_min_ind == 'ITEN') {
								$record_mecc['CODSPC'] = '';
							}
							if ($cod_min_ind == 'ITAT') {
								$record_mecc['CODSPC'] = '';
							}
							if ($cod_min_ind == 'ITMM') {
								$record_mecc['CODSPC'] = '';
							}
							if ($cod_min_ind == 'IT05') {
								$record_mecc['CODSPC'] = '';
							}
							if ($cod_min_ind == 'IT13') {
								$record_mecc['CODSPC'] = '';
							}
							if ($cod_min_ind == '0515') {
								$record_mecc['CODSPC'] = '';
							}
							if ($cod_min_ind == 'EA06') {
								$record_mecc['CODSPC'] = '';
								$record_mecc['CODSPR'] = 'IT01';
							}
							if ($cod_min_ind == 'Q362') {
								$record_mecc['CODSPC'] = '';
								$record_mecc['CODSPR'] = 'IPQP';
							}
							if($cod_min_ind == 'IT08'){
								$record_mecc['CODSPC'] = '';
								$record_mecc['CODSPR'] = 'ITET';
							}
							if (in_array($cod_min_ind, $codici_da_vuotare)) {
								$record_mecc['CODSPC'] = '';
							}

							if ($cod_min_ind == 'Q341') {
								$record_mecc['CODSPC'] = '';
								$record_mecc['CODSPR'] = 'IPQM';
							}

							if ($cod_min_ind == 'IP16') {
								$record_mecc['CODSPC'] = 'INGSPA';
								// $record_mecc['CODSPR'] = 'IPQM';
							}

							if ($cod_min_ind == 'Q305' && in_array($array_classi['classi'][$row['id_classe']]['classe'], ['1', '2'])) {
								$record_mecc['CODSPR'] = '0924';
							}

							if ($cod_min_ind == 'Q320' && in_array($array_classi['classi'][$row['id_classe']]['classe'], ['1', '2'])) {
								$record_mecc['CODSPC'] = 'OPRIPI';
								$record_mecc['CODSPR'] = 'IPQ3';
							}

							if($array_classi['classi'][$classe['id_classe']]['classificazione'] == 'MM'){
								$record_mecc['TIPSCU'] = 'MM';
								if($record_mecc['COSCUO'] == 'VI1M008007'){
									$record_mecc['CODSPR'] = 'ORDBIL';
								} elseif ($record_mecc['CODSPC'] == 'INGFRA' || $record_mecc['CODSPC'] == 'INGTED'){
									$record_mecc['CODSPR'] = '30';
								} elseif ($record_mecc['CODSPC'] == 'TEDESC'){
									$record_mecc['CODSPR'] = 'MUSICA';
								} elseif ($record_mecc['CODSPC'] == 'OOINGL'){
									$record_mecc['CODSPR'] = '30ORE';
								}else{
									$record_mecc['CODSPR'] = '';
								}
							}elseif($array_classi['classi'][$classe['id_classe']]['classificazione'] == 'EE'){
								$record_mecc['TIPSCU'] = 'EE';
								$record_mecc['CODSPR'] = '';
							}else{
								if($anno_classe > 2){
									$record_mecc['TIPSCU'] = 'NT';
								}else{
									$record_mecc['TIPSCU'] = 'NO';
								}
							}

							if ($record_mecc['CODSPC'] == 'LIB6') {
								$record_mecc['CODSPR'] = '';
							}

							if ($record_mecc['CODSPC'] == 'ITDT') {
								$record_mecc['CODSPR'] = 'ITAM';
								$record_mecc['CODSPC'] = '';
							}

							if ($record_mecc['CODSPR'] == 'ITDT') {
								$record_mecc['CODSPR'] = 'ITAM';
								$record_mecc['CODSPC'] = '';
							}

							if ($record_mecc['CODSPC'] == '30SETT'){
								$record_mecc['CODSPR'] = 'TEMLUN';
							}

							$usa_classid = 'NO';
							$elenco_classi[$classe['id_classe']]['adozioni'][] = $adozione;
							$elenco_classi[$classe['id_classe']]['SEZIONE'] = $array_classi['classi'][$classe['id_classe']]['sezione'];;
							$elenco_classi[$classe['id_classe']]['CLASSE']  = $array_classi['classi'][$classe['id_classe']]['classe'];
							$elenco_classi[$classe['id_classe']]['ALUNNI']  = $array_classi['classi'][$classe['id_classe']]['ALUNNI'];
							$elenco_classi[$classe['id_classe']]['ALUNNI']  = $array_classi_studenti[$classe['id_classe']];
							$elenco_classi[$classe['id_classe']]['CODSPC']  = $record_mecc['CODSPC'];
							$elenco_classi[$classe['id_classe']]['CODSPR']  = $record_mecc['CODSPR'];
							$elenco_classi[$classe['id_classe']]['TIPSCU']  = $record_mecc['TIPSCU'];


							if($usa_classid == 'SI'){
								$filter_classe = [];
								$filter_classe['classe'] = $anno_classe;
								$filter_classe['sezione'] = $sezione_classe;
								$filter_classe['codice_meccanografico'] = $codice_mecc_riferimento;
								$elenco_classi[$classe['id_classe']]['CLID']  = $record_mecc['CLID'];
							}else{
								$elenco_classi[$classe['id_classe']]['CLID']  = '';
							}

							if ($record_mecc['EMAIL'] == '') {
								$record_mecc['EMAIL'] = $email_sedi[$row['id_indirizzo']];
							}

							if ($record_mecc['TELEFO'] == ''){
								$record_mecc['TELEFO'] = $telefono_sedi[$row['id_indirizzo']];
							}

							$key_coscuo = $array_classi['classi'][$classe['id_classe']]['codice_meccanografico_secondario'];
							$elenco_classi[$classe['id_classe']]['COSCUO']  = $key_coscuo;

							if(count($record_mecc) > 0){
								$key_coscuo = $array_classi['classi'][$classe['id_classe']]['codice_meccanografico_secondario'];
								if (isset($record_mecc['COSCUO']) && $record_mecc['COSCUO'] != ''){ //in alcuni casi i dati arrivano mancanti o vuoti
									$array_codici[$key_coscuo]['COSCUO']  = $record_mecc['COSCUO'];
									$array_codici[$key_coscuo]['NOMSCU']  = $record_mecc['NOMSCU'];
									$array_codici[$key_coscuo]['INDSCU']  = $record_mecc['INDSCU'];
									$array_codici[$key_coscuo]['LOCSCU']  = $record_mecc['LOCSCU'];
									$array_codici[$key_coscuo]['CAPSCU']  = $record_mecc['CAPSCU'];
									$array_codici[$key_coscuo]['TELEFO']  = $record_mecc['TELEFO'];
									$array_codici[$key_coscuo]['FAX']     = $record_mecc['FAX'];
									$array_codici[$key_coscuo]['COSEDE']  = $record_mecc['COSEDE'];
									$array_codici[$key_coscuo]['EMAIL']   = $record_mecc['EMAIL'];
								}
							}
						}
					}
				}
			}
		}

		file_put_contents('/tmp/riga_rotta.txt', print_r($array_codici,true), FILE_APPEND);

		$numero_cod_scuola_file = count($array_codici);
		$codice_produttore = 'MAST_3.5.0';
		$data_ultimo_aggiornamento = date('dmY');


		// 'file014':
		//record di testa
		$file = "R001";
		$file .= date('dmY');
		$file .= str_pad($numero_cod_scuola_file, 3, " ", STR_PAD_LEFT);
		$file .= $codice_produttore;
		$file .= $data_ultimo_aggiornamento;
		$file .= "                              ";
		$file .= date('dmY');
		$file .= chr(13).chr(10);

		//$cont parte da due perchè considera già record di testa  e di coda
		$cont = 2;

		file_put_contents('/tmp/array_codici.txt', print_r($array_codici,true));
		foreach($array_codici as $singolo_record)
		{
			$nomscu_record = str_replace("à","a'", $singolo_record['NOMSCU']);
			$nomscu_record = str_replace("è","e'", $nomscu_record);
			$nomscu_record = str_replace("é","e'", $nomscu_record);
			$nomscu_record = str_replace("ì","i'", $nomscu_record);
			$nomscu_record = str_replace("ò","o'", $nomscu_record);
			$nomscu_record = str_replace("ù","u'", $nomscu_record);

			$indscu_record = str_replace("à","a'", $singolo_record['INDSCU']);
			$indscu_record = str_replace("è","e'", $indscu_record);
			$indscu_record = str_replace("é","e'", $indscu_record);
			$indscu_record = str_replace("ì","i'", $indscu_record);
			$indscu_record = str_replace("ò","o'", $indscu_record);
			$indscu_record = str_replace("ù","u'", $indscu_record);

			$locscu_record = str_replace("à","a'", $singolo_record['LOCSCU']);
			$locscu_record = str_replace("è","e'", $locscu_record);
			$locscu_record = str_replace("é","e'", $locscu_record);
			$locscu_record = str_replace("ì","i'", $locscu_record);
			$locscu_record = str_replace("ò","o'", $locscu_record);
			$locscu_record = str_replace("ù","u'", $locscu_record);

			//record del corpo
			$file .= "R014";
			$file .=  str_pad(trim(substr($singolo_record['COSCUO'],0,10)), 10);
			$file .=  str_pad('', 10);
			$file .=  str_pad(trim(substr($nomscu_record,0,50)), 50);
			$file .=  str_pad(trim(substr($indscu_record,0,35)), 35);
			$file .=  str_pad('', 30);
			$file .=  str_pad(trim(substr($locscu_record,0,35)), 35);
			$file .=  str_pad(trim(substr($singolo_record['CAPSCU'],0,5)), 5);
			$file .=  str_pad(trim(substr($singolo_record['TELEFO'],0,12)), 12);
			$file .=  str_pad(trim(substr($singolo_record['FAX'],0,12)), 12);
			$file .=  str_pad('', 1);
			$file .=  str_pad(trim(substr($singolo_record['COSEDE'],0,10)), 10);
			$file .=  str_pad(trim(substr($singolo_record['EMAIL'],0,80)), 80);
			$file .=  str_pad('', 50);
			$file .=  '*';
			$file .=  chr(13).chr(10);
			$cont++;
		}

		//record di coda
		$file .= "R099";
		$file .= date('dmY');
		$file .= str_pad(strval($cont), 5, " ", STR_PAD_LEFT);

		$dir = '/var/www-source/mastercom/tmp_export/';
		$file_name = 'file014.txt';
		$file = mb_convert_encoding($file, "ISO-8859-1");
		file_put_contents($dir.$file_name, $file);

		// 'file622':
		//record di testa
		$file = "R001";
		$file .= date('dmY');
		$file .= str_pad($numero_cod_scuola_file, 3, " ", STR_PAD_LEFT);
		$file .= chr(13).chr(10);

		//$cont parte da due perchè considera già record di testa  e di coda
		$cont = 2;
		//foreach($data['dati'] as $singolo_record)
		file_put_contents('/tmp/elenco_classi.txt', print_r($elenco_classi,true));
		foreach($elenco_classi as $singolo_record)
		{
			//record del corpo
			$file .= "R622";
			$file .=  str_pad(trim(substr($singolo_record['COSCUO'],0,10)), 10);
			$file .=  str_pad(trim(substr($singolo_record['SEZIONE'],0,4)), 4);
			$file .=  str_pad(trim(substr($singolo_record['TIPSCU'],0,2)), 2);
			//$file .=  str_pad('', 5);
			$file .=  str_pad(trim(substr($singolo_record['CLASSE'],0,1)), 1);
			$file .=  str_pad(trim(substr($singolo_record['ALUNNI'],0,2)), 2, " ", STR_PAD_LEFT);
			$file .=  str_pad(trim(substr($singolo_record['CODSPC'],0,6)), 6);
			$file .=  str_pad(trim(substr($singolo_record['CODSPR'],0,6)), 6);
			$file .=  str_pad('', 7);
			$file .=  str_pad('', 19);
			$file .=  str_pad(trim(substr($singolo_record['CLID'],0,9)), 9, " ", STR_PAD_LEFT);
			$file .=  '*';
			$file .= chr(13).chr(10);
			$cont++;
		}

		//record di coda
		$file .= "R099";
		$file .= date('dmY');
		$file .= str_pad($cont, 5, " ", STR_PAD_LEFT);

		$dir = '/var/www-source/mastercom/tmp_export/';
		$file_name = 'file622.txt';
		$file = mb_convert_encoding($file, "ISO-8859-1");
		file_put_contents($dir.$file_name, $file);


		// 'file815':
		//record di testa
		$file = "R001";
		$file .= date('dmY');
		$file .= str_pad($numero_cod_scuola_file, 3, " ", STR_PAD_LEFT);
		$file .= chr(13).chr(10);

		//$cont parte da due perchè considera già record di testa  e di coda
		$cont = 2;
		//foreach($data['dati'] as $singolo_record)

		foreach($elenco_classi as $singola_classe)
		//foreach($adozioni_totali as $singolo_record)
		{
			foreach($singola_classe['adozioni'] as $singolo_record)
			//foreach($adozioni_totali as $singolo_record)
			{

				if(
					(strlen(trim($singolo_record['ISBN13'])) > 0)
					&&
					(trim($singolo_record['APPROVAZIONE_SEGRETERIA']) == 'SI')
					&&
					(trim($singolo_record['APPROVAZIONE_RESPONSABILE']) == 'SI')
					&&
					($singolo_record['adozione_extra'] != "SI")
				)
				{
					if($singolo_record['ANNOADOZIONE'] == intval(date('Y'))){
						$singolo_record['INUSO'] = 'N';
						//$singolo_record['INPOSS'] = 'N';
					}else{
						$singolo_record['INUSO'] = 'S';
						//if($singolo_record['CONSIG2'] == 'SI'){
						//	$singolo_record['CONSIG'] = 'M';
						//	$singolo_record['INPOSS'] = 'S';
						//}
					}

					//if($singolo_record['dati_json']['fuori_catalogo'] != 'F'){

						/*if($singolo_record['INUSO'] == 'N')
						{
							$anno_adoz = '2020';
						}
						else
						{
							$anno_adoz = '    ';
						}*/
						//record del corpo
						$file .= "R815";
						$file .=  str_pad(trim(substr($singola_classe['COSCUO'],0,10)), 10);
						$file .=  str_pad(trim(substr($singola_classe['CLASSE'],0,1)), 1);
						$file .=  str_pad(trim(substr($singola_classe['SEZIONE'],0,4)), 4);
						$file .=  str_pad(trim(substr($singola_classe['TIPSCU'],0,2)), 2);
						$file .=  str_pad(trim(substr($singola_classe['CODSPC'],0,6)), 6);
						$file .=  str_pad(trim(substr($singola_classe['CODSPR'],0,6)), 6);
						$file .=  str_pad(trim(substr($singolo_record['CODPRO'],0,6)), 6);
						$file .=  str_pad(trim(substr($singolo_record['CONSIG'],0,1)), 1);
						$file .=  str_pad(trim(substr($singolo_record['INPOSS'],0,1)), 1);
						$file .=  str_pad('', 6);
						$file .=  str_pad(trim(substr($singolo_record['INUSO'],0,1)), 1);
						$file .=  str_pad(trim(substr($singolo_record['ISBN13'],0,13)), 13);
						$file .=  str_pad(trim(substr($singolo_record['ANNOADOZIONE'],0,4)), 4);
						$file .=  '*';
						$file .= chr(13).chr(10);
						$cont++;
					//}
				}
			}
		}

		//record di coda
		$file .= "R099";
		$file .= date('dmY');
		$file .= str_pad($cont, 5, " ", STR_PAD_LEFT);

		$dir = '/var/www-source/mastercom/tmp_export/';
		$file_name = 'file815.txt';
		$file = mb_convert_encoding($file, "ISO-8859-1");
		file_put_contents($dir.$file_name, $file);



		// 'file815e':
		//record di testa
		$file = "R001";
		$file .= date('dmY');
		$file .= str_pad($numero_cod_scuola_file, 3, " ", STR_PAD_LEFT);
		$file .= chr(13).chr(10);

		//$cont parte da due perchè considera già record di testa  e di coda
		$cont = 2;
		//foreach($data['dati'] as $singolo_record)
		foreach($elenco_classi as $singola_classe)
		//foreach($adozioni_totali as $singolo_record)
		{
			foreach($singola_classe['adozioni'] as $singolo_record)
			//foreach($adozioni_totali as $singolo_record)
			{

				if(
					(strlen(trim($singolo_record['ISBN13'])) > 0)
					&&
					(trim($singolo_record['APPROVAZIONE_SEGRETERIA']) == 'SI')
					&&
					(trim($singolo_record['APPROVAZIONE_RESPONSABILE']) == 'SI')
					)
				{
					if($singolo_record['ANNOADOZIONE'] == intval(date('Y'))){
						$singolo_record['INUSO'] = 'N';
						// $singolo_record['INPOSS'] = 'N';
					}else{
						$singolo_record['INUSO'] = 'S';
						//if($singolo_record['CONSIG2'] == 'SI'){
						//	$singolo_record['CONSIG'] = 'M';
						//	$singolo_record['INPOSS'] = 'S';
						//}
					}

					/*if($singolo_record['INUSO'] == 'N')
					{
						$anno_adoz = '2020';
					}
					else
					{
						$anno_adoz = '    ';
					}*/

					//nessun libro dentro questo file
					//if($singolo_record['dati_json']['fuori_catalogo'] == 'NULLA'){
					if ($singolo_record['adozione_extra'] == "SI") {
						//record del corpo
						$file .= "R81E";
						$file .=  str_pad(trim(substr($singola_classe['COSCUO'],0,10)), 10);
						$file .=  str_pad(trim(substr($singola_classe['CLASSE'],0,1)), 1);
						$file .=  str_pad(trim(substr($singola_classe['SEZIONE'],0,4)), 4);
						$file .=  str_pad(trim(substr($singola_classe['TIPSCU'],0,2)), 2);
						$file .=  str_pad(trim(substr($singola_classe['CODSPC'],0,6)), 6);
						$file .=  str_pad(trim(substr($singola_classe['CODSPR'],0,6)), 6);
						$file .=  str_pad(trim(substr($singolo_record['CONSIG'],0,1)), 1);
						$file .=  str_pad(trim(substr($singolo_record['INPOSS'],0,1)), 1);
						$file .=  str_pad(trim(substr($singolo_record['CODEDI'],0,6)), 6, " ", STR_PAD_LEFT);
						$file .=  str_pad(trim(substr($singolo_record['AUTORE'],0,30)), 30);
						$file .=  str_pad(trim(substr($singolo_record['TITOLO'],0,76)), 76);
						$file .=  str_pad(trim(substr($singolo_record['VOLUME'],0,1)), 1);
						$file .=  str_pad('', 30);
						$file .=  str_pad(trim(substr($singolo_record['CODMAT'],0,5)), 5, " ", STR_PAD_LEFT);
						$file .=  str_pad(trim(substr($singolo_record['INUSO'],0,1)), 1);
						$file .=  str_pad(trim(substr($singolo_record['ISBN13'],0,13)), 13);
						$file .=  str_pad(trim(substr($singolo_record['ANNOADOZIONE'],0,4)), 4);
						$file .=  '*';
						$file .= chr(13).chr(10);
						$cont++;
					}
				}
			}
		}

		if(strlen($data['anno_db']) > 0){
			$previous_old = $this->user->data->SetDb($previous);
		}

		//record di coda
		$file .= "R099";
		$file .= date('dmY');
		$file .= str_pad($cont, 5, " ", STR_PAD_LEFT);

		$dir = '/var/www-source/mastercom/tmp_export/';
		$file_name = 'file815e.txt';
		$file = mb_convert_encoding($file, "ISO-8859-1");
		file_put_contents($dir.$file_name, $file);



        $nome_export='exportAIE'.date('YmdHis').'.zip';
        exec ('zip -j '. $dir . $nome_export . ' ' . $dir . 'file*.txt');

		$results = $dir . $nome_export;
		return $results;
		/*}}}*/
	}

	/**
	 * Estrae dal database di AIE i dati aggiuntivi che servono per la compilazione finale dei file
     * @param array $data
	 * Il campo $data['dati'] deve contenere l'array dei codici meccanografici di cui si desidera estrarre i dati
	 * 	 */
	public function getInfoClassiSediAIE($data = null) {
		/*{{{ */

		$where = '(';
		if(is_array($data['dati'])){
			foreach ($data['dati'] as $codice){
				$where .= "'".$codice."',";
			}
			$where = substr($where,0,-1);
		}
		$where .= ')';

		$previous = $this->user->data->SetDb('adozioni_libri');
		$sql = "SELECT
				DISTINCT 	codice_meccanografico,
							--classe,
							codice_sperimentazione,
							codice_specializzazione,
							tipo_scuola,
							nome,
							indirizzo,
							comune,
							cap,
							telefono,
							fax,
							email,
							sito,
							COALESCE(scuola_id, '0') as scuola_id,
							COALESCE(classe_id, 0) as classe_id,
							COALESCE(classe, 0) as classe,
							COALESCE(sezione, '0') as sezione
							--scuola_id,
							--classe_id,
							--sezione
				FROM aie_scuola
				LEFT JOIN aie_classe ON aie_classe.scuola_id=aie_scuola.codice_meccanografico
				WHERE codice_meccanografico IN " . $where . "
				ORDER BY codice_meccanografico,classe,codice_specializzazione, codice_sperimentazione";

				file_put_contents('/tmp/sql_aie.txt', print_r($sql,true));
		$query = $this->user->data->db->prepare($sql);
		$query->execute();

		$rows = $query->fetchAll(PDO::FETCH_ASSOC);

		$codici_meccanografici = [];
		$previous_old = $this->user->data->SetDb($previous);

		foreach ($rows as $row){
			if(!is_array($codici_meccanografici[$row['codice_meccanografico']][$row['classe']][$row['sezione']])){
				$sezione_scelta = $row['sezione'];
			}else{
				$sezione_scelta = $row['sezione'] . random_int(1,100);
			}
				$codici_meccanografici[$row['codice_meccanografico']][$row['classe']][$sezione_scelta]['COSCUO'] = $row['codice_meccanografico'];
				$codici_meccanografici[$row['codice_meccanografico']][$row['classe']][$sezione_scelta]['TIPSCU'] = $row['tipo_scuola'];
				$codici_meccanografici[$row['codice_meccanografico']][$row['classe']][$sezione_scelta]['CODSPC'] = $row['codice_specializzazione'];
				$codici_meccanografici[$row['codice_meccanografico']][$row['classe']][$sezione_scelta]['CODSPR'] = $row['codice_sperimentazione'];
				$codici_meccanografici[$row['codice_meccanografico']][$row['classe']][$sezione_scelta]['NOMSCU'] = $row['nome'];
				$codici_meccanografici[$row['codice_meccanografico']][$row['classe']][$sezione_scelta]['INDSCU'] = $row['indirizzo'];
				$codici_meccanografici[$row['codice_meccanografico']][$row['classe']][$sezione_scelta]['LOCSCU'] = $row['comune'];
				$codici_meccanografici[$row['codice_meccanografico']][$row['classe']][$sezione_scelta]['CAPSCU'] = $row['cap'];
				$codici_meccanografici[$row['codice_meccanografico']][$row['classe']][$sezione_scelta]['TELEFO'] = $row['telefono'];
				$codici_meccanografici[$row['codice_meccanografico']][$row['classe']][$sezione_scelta]['FAX']    = $row['fax'];
				$codici_meccanografici[$row['codice_meccanografico']][$row['classe']][$sezione_scelta]['COSEDE'] = $row['scuola_id'];
				$codici_meccanografici[$row['codice_meccanografico']][$row['classe']][$sezione_scelta]['EMAIL']  = $row['email'];
				$codici_meccanografici[$row['codice_meccanografico']][$row['classe']][$sezione_scelta]['CLID']   = $row['classe_id'];
		}

		file_put_contents('/tmp/codici_meccanografici.txt', print_r($codici_meccanografici,true));
		$results = $codici_meccanografici;
		return $results;
		/*}}}*/
	}

	public function getClassiSucc($input = []){
		if (strlen($input['anno_db']) > 0) {
			$previous = $this->user->data->SetDb($input['anno_db']);
		}

		$sql = "SELECT
					c.id_classe,
					c.classe,
					c.sezione,
					i.id_indirizzo,
					i.codice,
					i.descrizione,
					c.num_stud_previsti_aie
				FROM
					classi_succ c
					INNER JOIN indirizzi_succ i ON i.id_indirizzo = c.id_indirizzo
				WHERE
					c.flag_canc = 0
					AND
					i.flag_canc = 0
				ORDER BY i.codice, c.classe, c.sezione";

		$query = $this->user->data->db->prepare($sql);
		$query->execute();

		$rows = $query->fetchAll(PDO::FETCH_ASSOC);

		if (count($rows) > 0){
			foreach ($rows as $key => $value){
				$rows[$key] = $this->user->data->DecodeField($value);
			}
		} else {
			$rows = [];
		}

		if (strlen($input['anno_db']) > 0) {
			$previous_old = $this->user->data->SetDb($previous);
		}

		return $rows;
	}

	public function salvaStudentiPrevistiClassiSucc($input){
		if (strlen($input['anno_db']) > 0) {
			$previous = $this->user->data->SetDb($input['anno_db']);
		}

		foreach ($input['studenti_previsti'] as $id_classe_succ => $studenti_previsti){
			if ($studenti_previsti >= 0){
				$sql = "UPDATE classi_succ SET num_stud_previsti_aie = {$studenti_previsti} WHERE id_classe = {$id_classe_succ}";
				$query = $this->user->data->db->prepare($sql);
				$query->execute();
			}
		}

		if (strlen($input['anno_db']) > 0) {
			$previous_old = $this->user->data->SetDb($previous);
		}

		return true;
	}
}
?>
