<?php
/********************
 * Roba disponibile:
 * $auth -> oggetto di autenticazione con dati utente autenticato e accesso db
 * $method -> GET/PUT/POST/DELETE o altre richieste http valide
 * $request -> array path richiesta (elemento 0 ha portato qua)
 * $query -> array variabili incluse nella richiesta
 * $input -> array variabili incluse nella richiesta
 *
 ********************/

require_once "models/class_module.php";// Implementa la classe Data generica di accesso ai dati mastercom
$module = new Module($auth);
if(count($body) > 0){
	$input_var = $body;
}else{
	$input_var = $input;
}
switch ($method) {
	case 'GET':
		/*{{{ */
		$inner_request = $request[1];

		switch ($inner_request) {
		case 'drafts':
			$result= $module->getDraftList();
			header('Content-type: application/json; charset=utf-8');
			echo json_encode($result);
			break;
		case 'templates':

			$filtro_template = $input_var;
			if ($request[2] == 'class') {
				$module_filter = [
					"selector" => [ "type" => "module_template", "status" => "active", "archived" => ['$exists' => false] ],
					"fields" => ['_id','status','nome','validita','pre_gen','funzione_preload'],
					"limit" => 5000];
				$class_filter = ['class' => $request[3]];
				//$result= $module->getTemplates($module_filter,$class_filter);
				$result= $module->getTemplates($filtro_template,$class_filter);
			} elseif ($request[2] == 'indirizzo') {
				$module_filter = [
					"selector" => [ "type" => "module_template", "status" => "active", "archived" => ['$exists' => false]],
					"fields" => ['_id','status','nome','validita','pre_gen','funzione_preload'],
					"limit" => 5000];
				if ($request[3] != 'tutti') {
					$class_filter = ['indirizzo' => $request[3]];
				} else {
					$class_filter = null;
				}
				//Logging::debug($module_filter);
				//Logging::debug($class_filter);
				//$result= $module->getTemplates($module_filter,$class_filter);
				$result= $module->getTemplates($filtro_template,$class_filter);
			} else {
				$result= $module->getTemplates($filtro_template);
			}

			header('Content-type: application/json; charset=utf-8');
			echo json_encode($result);
			break;
		case 'full':
			//Logging::debug($query,"ricerca");
				/* {{{ tutta la parte seguente viene ignorata con il passaggio alla vista */

				if (is_array($query['status'])) {
					foreach ($query['status'] as $value){
						$status['$or'][] = ['$eq' => $value];
					}
				} elseif(is_string($query['status'])) {
					$status = $query['status'];
				} else {
					$status = [ '$and' => [[ '$ne' => "deleted"],[ '$ne' => "draft"] ]];
				}
				$selector = ["type" => "module", "status" => $status, "archived" => ['$exists' => false]];
				if ($query['meeting']) {
					$selector['meeting'] = $query['meeting'];
				}
				if ($query['template']) {
					$selector['template_id'] = $query['template'];
				}
				if ($query['text']) {
					$text_elements = explode(' ',$query['text']);
					foreach ($text_elements as $element){
						$filter_elements[]['$or'] = [
							["nome" => [ '$regex' => "(?i).*".$element.".*"]],
							["label_utente" => [ '$regex' => "(?i).*".$element.".*"]],
							["insert_name" => [ '$regex' => "(?i).*".$element.".*"]]
						];
					}
					$selector['$and'] = $filter_elements;
				}


				if (isset($query['already_imported'])) {
					if ($query['already_imported'] == 'y') {
						$filter_elements = ['import_time' => ['$gt' => 0]];
					} else {
						$filter_elements = ['$or' => [['$not' => ['import_time' => ['$gt' => 0]]],['import_time' => ['$exists' => false]]]];

					}
					$selector['$and'][] = $filter_elements;

				}

				$filter = [
					"selector" => $selector,
					"sort" => [['insert_time' => 'desc']],
					"fields" => ['_id','status','insert_name','nome','insert_time','insert_date','modify_time','modify_date','import_time','import_date','label_utente','meeting','calendar'],
					"use_index" => "modules-index",
					"limit" => 5000
				];
				//$result = $module->getList($filter);

				/*}}}*/
				if (strlen($request[2]) > 0) {
					$result = $module->getList($query,'view',$request[2]);
				}else {
					$result = $module->getList($query,'view');
				}

				// Filtro per escludere moduli derivanti da template archiviati
				if (is_array($result) && count($result) > 0) {
					// Estraggo tutti i template_id unici dai risultati
					$template_ids = array_unique(array_column($result, 'template_id'));

					// Verifico quali template sono archiviati con una singola query
					$archived_templates = [];
					if (!empty($template_ids)) {
						$template_selector = [
							"selector" => [
								"type" => "module_template",
								"_id" => ['$in' => $template_ids],
								"archived" => true
							],
							"fields" => ["_id"]
						];
						$archived_result = $user->data->cdb->call('_find','POST',$template_selector);
						if (isset($archived_result['docs']) && is_array($archived_result['docs'])) {
							$archived_templates = array_column($archived_result['docs'], '_id');
						}
					}

					// Filtro i risultati escludendo i moduli con template archiviati
					$result = array_filter($result, function($row) use ($archived_templates) {
						return !in_array($row['template_id'], $archived_templates);
					});

					// Riordino gli indici dell'array
					$result = array_values($result);
				}

				foreach ($result as $key => $row){

					if (strlen($row['calendar'])>5) {
						$mat_calendar = explode(' ',$row['calendar']);
						$mat_date = explode('/',$mat_calendar[1]);
						$cal_translated = $mat_date[2].'/'.str_pad($mat_date[1],2,'0',STR_PAD_LEFT).'/'.str_pad($mat_date[0],2,0,STR_PAD_LEFT).' '.$mat_calendar[3].' '.$mat_calendar[0];
						if ($mat_calendar[7]) {
							$cal_translated .= ' '.$mat_calendar[7];
						} elseif ($mat_calendar[5]) {
							$cal_translated .= ' '.$mat_calendar[5];
						}
						$result[$key]['calendar_sortable'] = $cal_translated;
					}
				}

				if ($query['download']) {
					header('Content-Description: File Transfer');
					header('Content-Type: application/octet-stream');
					header('Content-Disposition: attachment; filename="export_modules.json"');
					header('Content-Transfer-Encoding: binary');
					header('Expires: 0');
					header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
					header('Pragma: public');
				} else {
					header('Content-type: application/json; charset=utf-8');
				}
				//Logging::debug($result);


				echo json_encode($result);
				//echo json_encode($module->getList(["selector" => ["type"=>"module","status"=>['$ne' =>"deleted"]]]));
				break;
		case null:
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($module->getList(null,'view'));
				break;
		default:
				if ($request[2] == 'reports') {
					$current_template = $module->get($inner_request);
					if ($current_template['_id']) {
						switch ($request[3]) {
						case 'class':
							$filter = [
								"class" => $request[4],
								"template" => $current_template['_id']
							];
							$result = $module->getReportModules($request[5],$filter);
							break;
						case 'indirizzo':
							if ($request[4] and $request[4] != 'tutti') {
								$filter = [
									"indirizzo" => $request[4],
									"template" => $current_template['_id']
								];
							} else {
								$filter = [
									"template" => $current_template['_id']
								];
							}
							//Logging::debug($filter);
							$result = $module->getReportModules($request[5],$filter);
							break;
						}

					} else {
						$result = $current_module;
					}
				} else {
					$result = $module->get($inner_request);
				}
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($result);
				break;
		}
		/*}}}*/
		break;
	case 'PUT':
		/*{{{ */

		$inner_request = $request[1];
		switch ($inner_request) {
		case "template":
			Logging::debug($input,'richiesta inserimento template');
			switch ($request[2]) {
			case 'simplemod_1':
				/*{{{ esempio di generazione template modulo monopagina dati una serie minima di campi obbligatori */
				$valido = true;
				$template = [];
				$id = null;
				if (isset($input['id'])) {
					$id = $input['id'];
				}
				$template['versione'] = '1';
				if (isset($input['titolo'])) {
					$template['nome'] = $input['titolo'];
				} else {
					$result['error'][] = 'titolo';
					$valido = false;
				}
				if (isset($input['gruppo'])) {
					$template['gruppo'] = $input['gruppo'];
				} else {
					$template['gruppo'] = 'Servizi';
				}
				$template['label_utente'] = '';
				if (isset($input['sottotitolo'])) {
					$template['messaggio'] = $input['sottotitolo'];
				} else {
					$result['error'][] = 'sottotitolo';
					$valido = false;
				}
				if (isset($input['validita'])) {
					$data_inizio = preg_match ('/(\d{2})\/(\d{2})\/(\d{4})\s(\d{2}):(\d{2})/' , $input['validita']['data_inizio'] , $mat_data_inizio );
					$data_fine = preg_match ('/(\d{2})\/(\d{2})\/(\d{4})\s(\d{2}):(\d{2})/' , $input['validita']['data_fine'] , $mat_data_fine );
					if ($data_inizio) {
						$template['validita']['inizio_validita'] = mktime(
							intval($mat_data_inizio[4]),
							intval($mat_data_inizio[5]),
							0,
							intval($mat_data_inizio[2]),
							intval($mat_data_inizio[1]),
							intval($mat_data_inizio[3])
						);
					} else {
						$template['validita']['inizio_validita'] = 0;
					}
					if ($data_fine) {
						$template['validita']['fine_validita'] = mktime(
							intval($mat_data_fine[4]),
							intval($mat_data_fine[5]),
							0,
							intval($mat_data_fine[2]),
							intval($mat_data_fine[1]),
							intval($mat_data_fine[3])
						);
					} else {
						$template['validita']['fine_validita'] = 2000000000;
					}

					if (strlen($input['validita']['tipi_indirizzo']) > 0) {
						$template['validita']['filtro']['tipi_indirizzo'] = explode(",", $input['validita']['tipi_indirizzo']);;
					}
					if (strlen($input['validita']['anni']) > 0) {
						$template['validita']['filtro']['anni'] = explode(",", $input['validita']['anni']);;
					}
					if (strlen($input['validita']['classi']) > 0) {
						$template['validita']['filtro']['classi'] = explode(",", $input['validita']['classi']);;
					}
				} else {
					$result['error'][] = 'validita';
					$valido = false;
				}
				$template['funzione_setup'] = 'defaultFiglioGenerale';

				$pagina['pagina'] = 'Modulo';
				if (isset($input['header'])) {
					$pagina['header'] = $input['header'];
				} else {
					$result['error'][] = 'header';
					$valido = false;
				}
				if (isset($input['footer'])) {
					$pagina['footer'] = $input['footer'];
				} else {
					$result['error'][] = 'footer';
					$valido = false;
				}
				$pagina['domande']["1"]["domanda"] = 'domanda_1';
				$pagina['domande']["1"]["riga"] = 1;
				$pagina['domande']["1"]["colonne"] = 12;
				$pagina['domande']["1"]["error"] = "Selezionare uno studente!";
				$pagina['domande']["1"]["placeholder"] = "Selezionare uno studente";
				$pagina['domande']["1"]["tipo"] = "select";
				$pagina['domande']["1"]["obbligatorio"] = true;
				$pagina['domande']["1"]["funzioni"]["default"] = "defaultFiglioGenerale";
				$pagina['domande']["1"]["funzioni"]["opzioni"] = "estraiListaFigliGenerale";
				$pagina['domande']["1"]["funzioni"]["azione_form"] = "registraStudente";
				$pagina['domande']["1"]["funzioni"]["registrazione"]["campo"] = "studente";
				$pagina['domande']["1"]["funzioni"]["registrazione"]["funzione"] = "";

				if (isset($input['testo_pre_scelta_figlio'])) {
					$pagina['domande']["1"]["header"] = $input['testo_pre_scelta_figlio'];
				} else {
					$result['error'][] = 'testo_pre_scelta_figlio';
					$valido = false;
				}

				$pagina['domande']["2"]["domanda"] = 'domanda_2';
				$pagina['domande']["2"]["riga"] = 2;
				$pagina['domande']["2"]["colonne"] = 12;
				$pagina['domande']["2"]["tipo"] = "message";
				$pagina['domande']["2"]["obbligatorio"] = false;

				if (isset($input['testo_post_scelta_figlio'])) {
					$pagina['domande']["2"]["header"] = $input['testo_post_scelta_figlio'];
				} else {
					$result['error'][] = 'testo_post_scelta_figlio';
					$valido = false;
				}


				$pagina['domande']["3"]["domanda"] = 'domanda_3';
				$pagina['domande']["3"]["riga"] = 3;
				$pagina['domande']["3"]["colonne"] = 12;
				$pagina['domande']["3"]["placeholder"] = "";
				$pagina['domande']["3"]["obbligatorio"] = true;
				$pagina['domande']["3"]["header"] = $input['testo_pre_domanda'];

				if ($input['tipo'] == 'solo conferma') {
					$pagina['domande']["3"]["error"] = "selezionare";
					$pagina['domande']["3"]["tipo"] = "check";
					$pagina['domande']["3"]["opzioni"]["1"]["testo"] = "Accetta";
					$pagina['domande']["3"]["opzioni"]["1"]["valore"] = "1";
					$pagina['domande']["3"]["opzioni"]["1"]["scelta"] = "";
					$pagina['domande']["3"]["opzioni"]["1"]["funzioni"]["statistiche"]["label"] = "Accetta";
					$pagina['domande']["3"]["opzioni"]["1"]["funzioni"]["statistiche"]["funzione"] = "";
					if (isset($input['campo_servizi'])) {
						$pagina['domande']["3"]["opzioni"]["1"]["funzioni"]["statistiche"]["campo"] = $input['campo_servizi'];
						$pagina['domande']["3"]["opzioni"]["1"]["funzioni"]["registrazione"]["campo"] = $input['campo_servizi'];
						$pagina['domande']["3"]["opzioni"]["1"]["funzioni"]["registrazione"]["funzione"] = "iscrizioneservizi";
					}
				} elseif ($input['tipo'] == 'radio' and strlen($input['opzioni'])>0) {
					$pagina['domande']["3"]["error"] = "selezionare un opzione";
					$pagina['domande']["3"]["tipo"] = "radio";
					$cont = 1;
					$mat_opzioni = explode(",",$input['opzioni']);
					foreach ($mat_opzioni as $key => $opzione){
						$pagina['domande']["3"]["opzioni"][$cont]["testo"] = $opzione;
						$pagina['domande']["3"]["opzioni"][$cont]["valore"] = $cont;
						$pagina['domande']["3"]["opzioni"][$cont]["scelta"] = "";
						$cont++;
					}
					if (isset($input['campo_servizi'])) {
						$pagina['domande']["3"]["funzioni"]["statistiche"]["campo"] = $input['campo_servizi'];
						$pagina['domande']["3"]["funzioni"]["registrazione"]["campo"] = $input['campo_servizi'];
						$pagina['domande']["3"]["funzioni"]["registrazione"]["funzione"] = "iscrizioneservizi";
					}
				} elseif ($input['tipo'] == 'check' and strlen($input['opzioni'])>0) {
					$pagina['domande']["3"]["error"] = "selezionare un opzione";
					$pagina['domande']["3"]["tipo"] = "check";
					$cont = 1;
					$mat_opzioni = explode(",",$input['opzioni']);
					foreach ($mat_opzioni as $key => $opzione){
						$pagina['domande']["3"]["opzioni"][$cont]["testo"] = $opzione;
						$pagina['domande']["3"]["opzioni"][$cont]["valore"] = $cont;
						$pagina['domande']["3"]["opzioni"][$cont]["scelta"] = "";
						if (isset($input['campo_servizi'])) {
							$pagina['domande']["3"]["opzioni"][$cont]["funzioni"]["statistiche"]["campo"] = $input['campo_servizi'];
							$pagina['domande']["3"]["opzioni"][$cont]["funzioni"]["registrazione"]["campo"] = $input['campo_servizi'];
							$pagina['domande']["3"]["opzioni"][$cont]["funzioni"]["registrazione"]["funzione"] = "iscrizioneservizi";
						}
						$cont++;
					}
				} else {
					$result['error'][] = 'no opzioni o solo_conferma';
					$valido = false;
				}
				$template['pagine']["1"] = $pagina;
				$template['type'] = 'module_template';
				$template['status'] = 'active';
				$template['pre_gen'] = $request[2];

				if ($valido) {
					$result = $module->setTemplate($id, $template);
				} else {
					$result['msg'] = 'campi obbligatori non compilati';
				}
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($result);
				/*}}}*/
				break;
			case 'genmod_1':
				/*{{{ esempio di generazione template modulo monopagina dati una serie minima di campi obbligatori */
				$valido = true;
				$template = [];
				$id = null;
				if (isset($input['id'])) {
					$id = $input['id'];
					$cosa = 'MODIFICA_MODULO';
				} else {
					$cosa = 'INSERIMENTO_MODULO';
				}
				$template['versione'] = '1';
				if (isset($input['titolo'])) {
					$template['nome'] = $input['titolo'];
				} else {
					$result['error'][] = 'titolo';
					$valido = false;
				}
				if (isset($input['gruppo'])) {
					$template['gruppo'] = $input['gruppo'];
				} else {
					$template['gruppo'] = 'Servizi';
				}
				if ($input['anno_scolastico']) {
					$template['anno_scolastico'] = $input['anno_scolastico'];
				}
				$template['label_utente'] = '';
				if (isset($input['sottotitolo'])) {
					$template['messaggio'] = $input['sottotitolo'];
				} else {
					$result['error'][] = 'sottotitolo';
					$valido = false;
				}
				if (isset($input['validita'])) {
					if ($input['validita']['data_inizio'] > 0) {
						$template['validita']['inizio_validita'] = $input['validita']['data_inizio'];
					} else {
						$template['validita']['inizio_validita'] = 0;
					}
					if ($input['validita']['data_fine'] > 0) {
						$template['validita']['fine_validita'] = $input['validita']['data_fine'];
					} else {
						$template['validita']['fine_validita'] = 2000000000;
					}

					if ($input['validita']['funzione']) {
						$template['validita']['funzione'] = $input['validita']['funzione'] ;
					}
					if (count($input['validita']['tipi_indirizzo'])>0) {
						$template['validita']['filtro']['tipi_indirizzo'] = $input['validita']['tipi_indirizzo'] ;
					}
					if (count($input['validita']['anni'])>0) {
						$template['validita']['filtro']['anni'] = $input['validita']['anni'] ;
					}
					if (count($input['validita']['classi'])>0) {
						$template['validita']['filtro']['classi'] = $input['validita']['classi'] ;
					}
				} else {
					$result['error'][] = 'validita';
					$valido = false;
				}
				$template['funzione_setup'] = 'defaultFiglioGenerale';


				$pagina['pagina'] = 'Modulo';
				if (isset($input['header'])) {
					$pagina['header'] = $input['header'];
				} else {
					$result['error'][] = 'header';
					$valido = false;
				}
				if (isset($input['footer'])) {
					$pagina['footer'] = $input['footer'];
				} else {
					$result['error'][] = 'footer';
					$valido = false;
				}
				$pagina['domande']["1"]["domanda"] = 'domanda_1';
				$pagina['domande']["1"]["riga"] = 1;
				$pagina['domande']["1"]["colonne"] = 12;
				$pagina['domande']["1"]["error"] = "Selezionare uno studente!";
				$pagina['domande']["1"]["placeholder"] = "Selezionare uno studente";
				$pagina['domande']["1"]["tipo"] = "select";
				$pagina['domande']["1"]["obbligatorio"] = true;
				$pagina['domande']["1"]["funzioni"]["default"] = "defaultFiglioGenerale";
				$pagina['domande']["1"]["funzioni"]["opzioni"] = "estraiListaFigliGenerale";
				$pagina['domande']["1"]["funzioni"]["azione_form"] = "registraStudente";
				$pagina['domande']["1"]["funzioni"]["registrazione"]["campo"] = "studente";
				$pagina['domande']["1"]["funzioni"]["registrazione"]["funzione"] = "";

				if (isset($input['testo_pre_scelta_figlio'])) {
					$pagina['domande']["1"]["header"] = $input['testo_pre_scelta_figlio'];
				} else {
					$result['error'][] = 'testo_pre_scelta_figlio';
					$valido = false;
				}

				$pagina['domande']["2"]["domanda"] = 'domanda_2';
				$pagina['domande']["2"]["riga"] = 2;
				$pagina['domande']["2"]["colonne"] = 12;
				$pagina['domande']["2"]["tipo"] = "message";
				$pagina['domande']["2"]["obbligatorio"] = false;

				if (isset($input['testo_post_scelta_figlio'])) {
					$pagina['domande']["2"]["header"] = $input['testo_post_scelta_figlio'];
				} else {
					$result['error'][] = 'testo_post_scelta_figlio';
					$valido = false;
				}


				$pagina['domande']["3"]["domanda"] = 'domanda_3';
				$pagina['domande']["3"]["riga"] = 3;
				$pagina['domande']["3"]["colonne"] = 12;
				$pagina['domande']["3"]["placeholder"] = "";
				$pagina['domande']["3"]["obbligatorio"] = true;
				$pagina['domande']["3"]["header"] = $input['testo_pre_domanda'];

				$pagina['domande']["3"]["error"] = "selezionare";
				$pagina['domande']["3"]["tipo"] = "check";
				$pagina['domande']["3"]["opzioni"]["1"]["testo"] = "Accetta";
				$pagina['domande']["3"]["opzioni"]["1"]["valore"] = "1";
				$pagina['domande']["3"]["opzioni"]["1"]["scelta"] = "";
				$pagina['domande']["3"]["opzioni"]["1"]["funzioni"]["statistiche"]["label"] = "Accetta";
				$pagina['domande']["3"]["opzioni"]["1"]["funzioni"]["statistiche"]["funzione"] = "iscrizioneservizi";
				if (isset($input['campo_servizi'])) {
					$pagina['domande']["3"]["opzioni"]["1"]["funzioni"]["statistiche"]["campo"] = $input['campo_servizi'];
					$pagina['domande']["3"]["opzioni"]["1"]["funzioni"]["registrazione"]["campo"] = $input['campo_servizi'];
					$pagina['domande']["3"]["opzioni"]["1"]["funzioni"]["registrazione"]["funzione"] = "iscrizioneservizi";
				}

				if ($input['permetti_note'] == 'on') {
					$pagina['domande']["4"]["domanda"] = 'domanda_4';
					$pagina['domande']["4"]["header"] = 'Note';
					$pagina['domande']["4"]["riga"] = 4;
					$pagina['domande']["4"]["colonne"] = 12;
					$pagina['domande']["4"]["tipo"] = "area";
					$pagina['domande']["4"]["obbligatorio"] = false;
					$pagina['domande']["4"]["funzioni"]["statistiche"]["label"] = "Note";
					$pagina['domande']["4"]["funzioni"]["statistiche"]["funzione"] = "iscrizioneservizi";
					$pagina['domande']["4"]["funzioni"]["statistiche"]["campo"] = 'note';
					$pagina['domande']["4"]["funzioni"]["registrazione"]["campo"] = 'note';
					$pagina['domande']["4"]["funzioni"]["registrazione"]["funzione"] = "iscrizioneservizi";
				}

				$template['pagine']["1"] = $pagina;
				$template['type'] = 'module_template';
				$template['status'] = 'active';
				$template['pre_gen'] = $request[2];

				if ($valido) {
					$result = $module->setTemplate($id, $template);
					$module->user->inserisciLogStorico("MODELLI_MODULI", $cosa . " " . $result['id'], "", $dato = $input);
				} else {
					$result['msg'] = 'campi obbligatori non compilati';
				}
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($result);
				/*}}}*/
				break;
			case 'genmod_2':
				/*{{{ esempio di generazione template modulo monopagina dati una serie minima di campi obbligatori */
				$valido = true;
				$template = [];
				$id = null;
				if (isset($input['id'])) {
					$id = $input['id'];
					$cosa = 'MODIFICA_MODULO';
				} else {
					$cosa = 'INSERIMENTO_MODULO';
				}
				$template['versione'] = '1';
				if (isset($input['titolo'])) {
					$template['nome'] = $input['titolo'];
				} else {
					$result['error'][] = 'titolo';
					$valido = false;
				}
				if (isset($input['gruppo'])) {
					$template['gruppo'] = $input['gruppo'];
				} else {
					$template['gruppo'] = 'Servizi';
				}
				if ($input['anno_scolastico']) {
					$template['anno_scolastico'] = $input['anno_scolastico'];
				}
				$template['label_utente'] = '';
				if (isset($input['sottotitolo'])) {
					$template['messaggio'] = $input['sottotitolo'];
				} else {
					$result['error'][] = 'sottotitolo';
					$valido = false;
				}
				if (isset($input['validita'])) {
					if ($input['validita']['data_inizio'] > 0) {
						$template['validita']['inizio_validita'] = $input['validita']['data_inizio'];
					} else {
						$template['validita']['inizio_validita'] = 0;
					}
					if ($input['validita']['data_fine'] > 0) {
						$template['validita']['fine_validita'] = $input['validita']['data_fine'];
					} else {
						$template['validita']['fine_validita'] = 2000000000;
					}

					if ($input['validita']['funzione']) {
						$template['validita']['funzione'] = $input['validita']['funzione'] ;
					}
					if (count($input['validita']['tipi_indirizzo'])>0) {
						$template['validita']['filtro']['tipi_indirizzo'] = $input['validita']['tipi_indirizzo'] ;
					}
					if (count($input['validita']['anni'])>0) {
						$template['validita']['filtro']['anni'] = $input['validita']['anni'] ;
					}
					if (count($input['validita']['classi'])>0) {
						$template['validita']['filtro']['classi'] = $input['validita']['classi'] ;
					}
				} else {
					$result['error'][] = 'validita';
					$valido = false;
				}
				$template['funzione_setup'] = 'defaultFiglioGenerale';


				$pagina['pagina'] = 'Modulo';
				if (isset($input['header'])) {
					$pagina['header'] = $input['header'];
				} else {
					$result['error'][] = 'header';
					$valido = false;
				}
				if (isset($input['footer'])) {
					$pagina['footer'] = $input['footer'];
				} else {
					$result['error'][] = 'footer';
					$valido = false;
				}
				$pagina['domande']["1"]["domanda"] = 'domanda_1';
				$pagina['domande']["1"]["riga"] = 1;
				$pagina['domande']["1"]["colonne"] = 12;
				$pagina['domande']["1"]["error"] = "Selezionare uno studente!";
				$pagina['domande']["1"]["placeholder"] = "Selezionare uno studente";
				$pagina['domande']["1"]["tipo"] = "select";
				$pagina['domande']["1"]["obbligatorio"] = true;
				$pagina['domande']["1"]["funzioni"]["default"] = "defaultFiglioGenerale";
				$pagina['domande']["1"]["funzioni"]["opzioni"] = "estraiListaFigliGenerale";
				$pagina['domande']["1"]["funzioni"]["azione_form"] = "registraStudente";
				$pagina['domande']["1"]["funzioni"]["registrazione"]["campo"] = "studente";
				$pagina['domande']["1"]["funzioni"]["registrazione"]["funzione"] = "";

				if (isset($input['testo_pre_scelta_figlio'])) {
					$pagina['domande']["1"]["header"] = $input['testo_pre_scelta_figlio'];
				} else {
					$result['error'][] = 'testo_pre_scelta_figlio';
					$valido = false;
				}

				$pagina['domande']["2"]["domanda"] = 'domanda_2';
				$pagina['domande']["2"]["riga"] = 2;
				$pagina['domande']["2"]["colonne"] = 12;
				$pagina['domande']["2"]["tipo"] = "message";
				$pagina['domande']["2"]["obbligatorio"] = false;

				if (isset($input['testo_post_scelta_figlio'])) {
					$pagina['domande']["2"]["header"] = $input['testo_post_scelta_figlio'];
				} else {
					$result['error'][] = 'testo_post_scelta_figlio';
					$valido = false;
				}


				$pagina['domande']["3"]["domanda"] = 'domanda_3';
				$pagina['domande']["3"]["riga"] = 3;
				$pagina['domande']["3"]["colonne"] = 12;
				$pagina['domande']["3"]["placeholder"] = "";
				$pagina['domande']["3"]["obbligatorio"] = true;
				$pagina['domande']["3"]["header"] = $input['testo_pre_domanda'];
				$pagina['domande']["3"]["error"] = "selezionare un opzione";
				$pagina['domande']["3"]["tipo"] = "radio";
					Logging::debug($input['opzioni'],'opzioni modulo_inserito');
				foreach ($input['opzioni'] as $key => $opzione){

					$pagina['domande']["3"]["opzioni"][$key]["testo"] = $opzione['desc'];
					$pagina['domande']["3"]["opzioni"][$key]["valore"] = $opzione['val'];
					$pagina['domande']["3"]["opzioni"][$key]["scelta"] = "";
				}
				if (isset($input['campo_servizi'])) {
					$pagina['domande']["3"]["funzioni"]["statistiche"]["campo"] = $input['campo_servizi'];
					$pagina['domande']["3"]["funzioni"]["registrazione"]["campo"] = $input['campo_servizi'];
					$pagina['domande']["3"]["funzioni"]["registrazione"]["funzione"] = "iscrizioneservizi";
				}

				if ($input['permetti_note'] == 'on') {
					$pagina['domande']["4"]["domanda"] = 'domanda_4';
					$pagina['domande']["4"]["header"] = 'Note';
					$pagina['domande']["4"]["riga"] = 4;
					$pagina['domande']["4"]["colonne"] = 12;
					$pagina['domande']["4"]["tipo"] = "area";
					$pagina['domande']["4"]["obbligatorio"] = false;
					$pagina['domande']["4"]["funzioni"]["statistiche"]["label"] = "Note";
					$pagina['domande']["4"]["funzioni"]["statistiche"]["funzione"] = "iscrizioneservizi";
					$pagina['domande']["4"]["funzioni"]["statistiche"]["campo"] = 'note';
					$pagina['domande']["4"]["funzioni"]["registrazione"]["campo"] = 'note';
					$pagina['domande']["4"]["funzioni"]["registrazione"]["funzione"] = "iscrizioneservizi";
				}

				$template['pagine']["1"] = $pagina;
				$template['type'] = 'module_template';
				$template['status'] = 'active';
				$template['pre_gen'] = $request[2];

				if ($valido) {
					$result = $module->setTemplate($id, $template);
					$module->user->inserisciLogStorico("MODELLI_MODULI", $cosa . " " . $result['id'], "", $dato = $input);
				} else {
					$result['msg'] = 'campi obbligatori non compilati';
				}
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($result);
				/*}}}*/
				break;
			default:
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($module->setTemplate($request[2], $input));
				break;
			}
			break;
		case "draft":
			header('Content-type: application/json; charset=utf-8');
			echo json_encode($module->setDraft($request[2], $input));
			break;
		case null:
		default:
			$result = $module->set($inner_request, $input);
			Logging::debug($result,'modulo_inserito');
			if ($result !== false) {
				if ($result['ok']) {
					$data = $module->get($result['id']);
					$send = $auth->data->GetParametro('notifiche_moduli_invia_mail_submitted');
					Logging::debug($send,"parametro \n");

					if ($send != 'NO') {
						require_once 'models/class_contact.php';
						$contact = new Contact($auth);
						$destinatario = $contact->getContactPrivateData($data['owner_id']);
						Logging::debug($destinatario,"destinatario \n");

						if (strlen($destinatario[0]['email']) > 3) {
							require_once 'models/class_mail.php';
							$mail = new Mailer($auth);
							$to = [['email' => $destinatario[0]['email'], 'name' => $destinatario[0]['cognome'] .' '.$destinatario[0]['nome']]];
							$subject = $auth->data->GetParametro('notifiche_moduli_header_submitted');
							$message = $auth->data->GetParametro('notifiche_moduli_testo_submitted');
							Logging::debug($mail->set($to,$subject,$message));
						}
					}
				}
			}
			header('Content-type: application/json; charset=utf-8');
			echo json_encode($result);
			break;
		}
		/*}}}*/
		break;
	case 'POST':
		/*{{{ */
		switch ($request[1]) {
		case 'bulk_delete':
			/*{{{ */
			$list_modules = $body['list'];
			if (is_array($list_modules)) {
				foreach ($list_modules as $id) {
					$results[] = $module->delete($id, 'trashcan');
				}
			} else {
				$results = 'nu-hu';
			}
			header('Content-type: application/json; charset=utf-8');
			echo json_encode($results);
			/*}}}*/
			break;
		case 'bulk_archive':
			/*{{{ */
			$list_modules = $body['list'];
			if (is_array($list_modules)) {
				foreach ($list_modules as $id) {
					$results[] = $module->archiveTemplate($id);
				}
			} else {
				$results = 'nu-hu';
			}
			header('Content-type: application/json; charset=utf-8');
			echo json_encode($results);
			/*}}}*/
			break;
		case 'empty_trash':
			/*{{{ */
			$list_modules = $body['list'];
			if (is_array($list_modules)) {
				foreach ($list_modules as $id) {
					$results[] = $module->delete($id, 'archive');
				}
			} else {
				$results = 'nu-hu';
			}
			header('Content-type: application/json; charset=utf-8');
			echo json_encode($results);
			/*}}}*/
			break;
		case 'template':
			/*{{{ */
			$id = $request[2];
			$data = $module->get($id);
			if ($data['_id']) {
				if (is_numeric($request[3])) {
					// sto operando su una pagina del template
					if (is_numeric($request[4])) {
						//sto operando su una domanda di una pagina del template

					} else {
						//editing dell'header della pagina

					}
				} else {
					// editing dell'header del modulo
					Logging::debug($input,"Input: \n\n");
					if ($input['titolo']) {
						$data['nome'] = $input['titolo'] ;
					}
					if ($input['sottotitolo']) {
						$data['messaggio'] = $input['sottotitolo'] ;
					}
					if ($input['anno_scolastico']) {
						$data['anno_scolastico'] = $input['anno_scolastico'] ;
					}
					if ($input['status']) {
						$data['status'] = $input['status'] ;
					}

					if (strlen($input['funzione_setup']) >0) {
						$data['funzione_setup'] = $input['funzione_setup'] ;
					}else {
						unset($data['funzione_setup']);
					}
					if (strlen($input['funzione_preload']) >0) {
						$data['funzione_preload'] = $input['funzione_preload'] ;
					}else {
						unset($data['funzione_preload']);
					}
					if (strlen($input['funzione_validazione']) >0) {
						$data['funzione_validazione'] = $input['funzione_validazione'] ;
					}else {
						unset($data['funzione_validazione']);
					}

					if ($input['validita']) {
						if ($input['validita']['data_inizio']) {
							$data['validita']['inizio_validita'] = $input['validita']['data_inizio'] ;
						}
						if ($input['validita']['data_fine']) {
							$data['validita']['fine_validita'] = $input['validita']['data_fine'] ;
						}
						if ($input['validita']['funzione']) {
							$data['validita']['funzione'] = $input['validita']['funzione'] ;
						} else {
							unset($data['validita']['funzione']);
						}
						if (count($input['validita']['tipi_indirizzo'])>0) {
							$data['validita']['filtro']['tipi_indirizzo'] = $input['validita']['tipi_indirizzo'] ;
						} else {
							unset($data['validita']['filtro']['tipi_indirizzo']);
						}
						if (count($input['validita']['anni'])>0) {
							$data['validita']['filtro']['anni'] = $input['validita']['anni'] ;
						} else {
							unset($data['validita']['filtro']['anni']);
						}
						if (count($input['validita']['classi'])>0) {
							$data['validita']['filtro']['classi'] = $input['validita']['classi'] ;
						} else {
							unset($data['validita']['filtro']['classi']);
						}
					}
					if ($input['permessi']) {
						if (count($input['permessi']['utenti'])>0) {
							$data['permessi']['utenti'] = $input['permessi']['utenti'] ;
						} else {
							unset($data['permessi']['utenti']);
						}
					} else {
						unset($data['permessi']);
					}
					Logging::debug($data,"Data: \n\n");
					$results = $module->setTemplate($id, $data);

				}
			} else {
				//il modulo che vogliono editare non esiste (per creazione si usa altra api)
				$results = [];
			}

			header('Content-type: application/json; charset=utf-8');
			echo json_encode($results);
			/*}}}*/
			break;
		default:
			switch ($request[2]) {
			case 'process':
				/*{{{ */
				$result = $module->processModule($request[1], $input['feedback']);
				if ($result !== false) {
					if ($result['ok']) {
						$data = $module->get($request[1]);
						if ($data['_id'] == $request[1]) {
							$send = $auth->data->GetParametro('notifiche_moduli_invia_mail_process');
							$send_mod = $data['notifiche']['invia_mail_process'];
							if (($send == 'SI' and $send_mod != 'NO') or $send_mod == 'SI') {
								require_once 'models/class_contact.php';
								$contact = new Contact($auth);
								$destinatario = $contact->getContactPrivateData($data['owner_id']);
								// Logging::debug($destinatario,"test destinatoraio modulo festa: \n\n");

								$oggetto_messaggio = $auth->data->GetParametro('notifiche_moduli_header_process') . " (Rif.: " . $data['nome'] . " per " . $data['label_utente'] . ")";
								$corpo_messaggio = $auth->data->GetParametro('notifiche_moduli_testo_process') . "<br>Rif. modulo: " . $data['label_utente'] . "<br>-------------------<br><br>" . $data['feedback'];

								// invio messaggio messenger
								require_once 'models/class_messaggistica.php';
								$messaggistica = new Messaggistica($auth);
								$dati_messaggio = [
									'oggetto' => $oggetto_messaggio,
									'corpo' => $corpo_messaggio,
									"id_mittente" => $auth->type['id'],
									"tipo_mittente" => $auth->type['type'],
									"destinatari" => [
										[
											"id" => $destinatario[0]['id_parente'],
											"tipo" => 'parente'
										]
									]
								];
								$invio_messaggio = $messaggistica->inserisciMessaggio($dati_messaggio);

								// Se ci sono allegati, mando anche mail
								$attachment = $auth->data->GetParametro('notifiche_moduli_allegato_mail_process');
								$attachment_mod = $data['notifiche']['allegato_mail_process'];
								if (($attachment == 'SI' and $attachment_mod != 'NO') or $attachment_mod == 'SI') {
									$input['invia_mail'] = 'SI';
									$input['invia_mail_stesso_testo'] = 'SI';
								}

								if ($input['invia_mail'] == 'SI'){
									if (strlen($destinatario[0]['email']) > 3) {
										require_once 'models/class_mail.php';
										$mail = new Mailer($auth);
										$to = [['email' => $destinatario[0]['email'], 'name' => $destinatario[0]['cognome'] .' '.$destinatario[0]['nome']]];
										$subject = $oggetto_messaggio;
										if ($input['invia_mail_stesso_testo'] == 'SI'){
											$message = $corpo_messaggio;
										} else {
											$message = "Hai ricevuto un nuovo messaggio, puoi visualizzarlo nella Messaggistica del Registro Elettronico MasterCom";
										}
										$school = new School($auth);

										require_once 'models/class_printer.php';
										$printer = new Printer($auth, $school);

										// $attachment = $auth->data->GetParametro('notifiche_moduli_allegato_mail_process');
										// $attachment_mod = $data['notifiche']['allegato_mail_process'];

										if (($attachment == 'SI' and $attachment_mod != 'NO') or $attachment_mod == 'SI') {
											require_once 'models/class_printer.php';
											$printer = new Printer($auth, $school);
											$attachments[$data['nome'].".pdf"] = $printer->print_module($data, $pagina, $body, 'S');
										}

										$mail->set($to,$subject,$message,null,null,$attachments);
										Logging::debug($mail,"test mail modulo festa: \n\n");
									}
								}
							}
						}
					}
				}
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($result);
				/*}}}*/
				break;
			case 'reject':
				/*{{{ */
				$result = $module->rejectModule($request[1], $input['feedback']);
				if ($result !== false) {
					if ($result['ok']) {
						$data = $module->get($request[1]);
						if ($data['_id'] == $request[1]) {
							$send = $auth->data->GetParametro('notifiche_moduli_invia_mail_reject');
							if ($send != 'NO') {
								require_once 'models/class_contact.php';
								$contact = new Contact($auth);
								$destinatario = $contact->getContactPrivateData($data['owner_id']);

								$oggetto_messaggio = $auth->data->GetParametro('notifiche_moduli_header_reject') . " (Rif.: " . $data['nome'] . " per " . $data['label_utente'] . ")";
								$corpo_messaggio = $auth->data->GetParametro('notifiche_moduli_testo_reject') . "<br>Rif. modulo: " . $data['label_utente'] . "<br>-------------------<br><br>" . $data['feedback'];

								// invio messaggio messenger
								require_once 'models/class_messaggistica.php';
								$messaggistica = new Messaggistica($auth);
								$dati_messaggio = [
									'oggetto' => $oggetto_messaggio,
									'corpo' => $corpo_messaggio,
									"id_mittente" => $auth->type['id'],
									"tipo_mittente" => $auth->type['type'],
									"destinatari" => [
										[
											"id" => $destinatario[0]['id_parente'],
											"tipo" => 'parente'
										]
									]
								];
								$invio_messaggio = $messaggistica->inserisciMessaggio($dati_messaggio);

								if ($input['invia_mail'] == 'SI') {
									if (strlen($destinatario[0]['email']) > 3) {
										require_once 'models/class_mail.php';
										$mail = new Mailer($auth);
										$to = [['email' => $destinatario[0]['email'], 'name' => $destinatario[0]['cognome'] .' '.$destinatario[0]['nome']]];
										$subject = $oggetto_messaggio;
										if ($input['invia_mail_stesso_testo'] == 'SI'){
											$message = $corpo_messaggio;
										} else {
											$message = "Hai ricevuto un nuovo messaggio, puoi visualizzarlo nella Messaggistica del Registro Elettronico MasterCom";
										}
										$mail->set($to,$subject,$message);
									}
								}
							}
						}
					}
				}
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($result);
				/*}}}*/
				break;
			case 'comment':
				/*{{{ */
				$result = $module->commentModule($request[1], $input['feedback']);
				if ($result !== false) {
					if ($result['ok']) {
						$data = $module->get($request[1]);
						if ($data['_id'] == $request[1]) {
							$send = $auth->data->GetParametro('notifiche_moduli_invia_mail_comment');
							if ($send != 'NO') {
								require_once 'models/class_contact.php';
								$contact = new Contact($auth);
								$destinatario = $contact->getContactPrivateData($data['owner_id']);

								$oggetto = $auth->data->GetParametro('notifiche_moduli_header_comment') . " (Rif.: " . $data['nome'] . " per " . $data['label_utente'] . ")";
								$corpo = $auth->data->GetParametro('notifiche_moduli_testo_comment') . "<br>Rif. modulo: " . $data['label_utente'] . "<br>-------------------<br><br>" . $data['feedback'];

								// invio messaggio messenger
								require_once 'models/class_messaggistica.php';
								$messaggistica = new Messaggistica($auth);
								$dati_messaggio = [
									'oggetto' => $oggetto,
									'corpo' => $corpo,
									"id_mittente" => $auth->type['id'],
									"tipo_mittente" => $auth->type['type'],
									"destinatari" => [
										[
											"id" => $destinatario[0]['id_parente'],
											"tipo" => 'parente'
										]
									]
								];
								$invio_messaggio = $messaggistica->inserisciMessaggio($dati_messaggio);

								if ($input['invia_mail'] == 'SI') {
									if (strlen($destinatario[0]['email']) > 3) {
										require_once 'models/class_mail.php';
										$mail = new Mailer($auth);
										$to = [['email' => $destinatario[0]['email'], 'name' => $destinatario[0]['cognome'] .' '.$destinatario[0]['nome']]];
										$subject = $oggetto;
										if ($input['invia_mail_stesso_testo'] == 'SI'){
											$message = $corpo;
										} else {
											$message = "Hai ricevuto un nuovo messaggio, puoi visualizzarlo nella Messaggistica del Registro Elettronico MasterCom";
										}
										$mail->set($to,$subject,$message);
									}
								}
							}
						}
					}
				}
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($result);
				/*}}}*/
				break;
			case 'ask-meeting':
				/*{{{ */
				// mando sia messaggio che mail perche' non c'e' interfaccia di scelta
				$result = $module->askMeetingModule($request[1], $input['feedback']);
				if ($result !== false) {
					if ($result['ok']) {
						$data = $module->get($request[1]);
						if ($data['_id'] == $request[1]) {
							$send = $auth->data->GetParametro('notifiche_moduli_invia_mail_meeting');
							if ($send != 'NO') {
								require_once 'models/class_contact.php';
								$contact = new Contact($auth);
								$destinatario = $contact->getContactPrivateData($data['owner_id']);

								$oggetto = $auth->data->GetParametro('notifiche_moduli_header_meeting') . " (Rif.: " . $data['nome'] . " per " . $data['label_utente'] . ")" . " (Rif.: " . $data['nome'] . " per " . $data['label_utente'] . ")";
								$corpo = $auth->data->GetParametro('notifiche_moduli_testo_meeting') . "<br>Rif. modulo: " . $data['label_utente'] . "<br>-------------------<br><br>" . $data['feedback'];

								// invio messaggio messenger
								require_once 'models/class_messaggistica.php';
								$messaggistica = new Messaggistica($auth);
								$dati_messaggio = [
									'oggetto' => $oggetto,
									'corpo' => $corpo,
									"id_mittente" => $auth->type['id'],
									"tipo_mittente" => $auth->type['type'],
									"destinatari" => [
										[
											"id" => $destinatario[0]['id_parente'],
											"tipo" => 'parente'
										]
									]
								];
								$invio_messaggio = $messaggistica->inserisciMessaggio($dati_messaggio);

								if (strlen($destinatario[0]['email']) > 3) {
									require_once 'models/class_mail.php';
									$mail = new Mailer($auth);
									$to = [['email' => $destinatario[0]['email'], 'name' => $destinatario[0]['cognome'] .' '.$destinatario[0]['nome']]];
									$subject = $oggetto;
									$message = $corpo;
									$mail->set($to,$subject,$message);
								}
							}
						}
					}
				}
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($result);
				/*}}}*/
				break;
			case 'reserve-meeting':
				/*{{{ */
				$result = $module->reserveMeetingModule($request[1], $input['selected_slot'], $input['selected_date']);
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($result);
				/*}}}*/
				break;
			case 'submit':
				/*{{{ */
				// mando sia messaggio che mail perche' non c'e' interfaccia di scelta
				$result = $module->submitModule($request[1]);
				if ($result !== false) {
					if ($result['ok']) {
						$data = $module->get($request[1]);
						if ($data['_id'] == $request[1]) {
							$send = $auth->data->GetParametro('notifiche_moduli_invia_mail_submitted');
							if ($send != 'NO') {
								require_once 'models/class_contact.php';
								$contact = new Contact($auth);
								$destinatario = $contact->getContactPrivateData($data['owner_id']);

								$oggetto = $auth->data->GetParametro('notifiche_moduli_header_submitted') . " (Rif.: " . $data['nome'] . " per " . $data['label_utente'] . ")";
								$corpo = $auth->data->GetParametro('notifiche_moduli_testo_submitted') . "<br>Rif. modulo: " . $data['label_utente'] . "<br>-------------------<br><br>" . $data['feedback'];

								// invio messaggio messenger
								require_once 'models/class_messaggistica.php';
								$messaggistica = new Messaggistica($auth);
								$dati_messaggio = [
									'oggetto' => $oggetto,
									'corpo' => $corpo,
									"id_mittente" => $auth->type['id'],
									"tipo_mittente" => $auth->type['type'],
									"destinatari" => [
										[
											"id" => $destinatario[0]['id_parente'],
											"tipo" => 'parente'
										]
									]
								];
								$invio_messaggio = $messaggistica->inserisciMessaggio($dati_messaggio);

								if (strlen($destinatario[0]['email']) > 3) {
									require_once 'models/class_mail.php';
									$mail = new Mailer($auth);
									$to = [['email' => $destinatario[0]['email'], 'name' => $destinatario[0]['cognome'] .' '.$destinatario[0]['nome']]];
									$subject = $oggetto;
									$message = $corpo;
									$mail->set($to,$subject,$message);
								}
							}
						}
					}
				}
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($result);
				/*}}}*/
				break;
			case 'unsubmit':
				/*{{{ */
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($module->unsubmitModule($request[1]));
				/*}}}*/
				break;
			case 'import':
				/*{{{ */
				$data = $module->get($request[1]);
				if ($data['_id'] == $request[1]) {
					$import['module'] = $data;
					if (!($data['import_time'] > 0) or true) {
						$school = new School($auth);
						$import['result'] = $school->importaModuloInMastercom($data);
					} else {
						$import['error'] = 'modulo gia importato';
					}
				} else {
					$import['module'] = $data;
					$import['module']['_id'] = $request[1];
					$import['error'] = 'modulo inesistente';
				}
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($import);
				/*}}}*/
				break;
			case "restore":
				$data = $module->get($request[1]);
				if ($data['_id'] == $request[1]) {
					$undelete = $module->softUnDelete($request[1]);
				} else {
					$undelete['module'] = $data;
					$undelete['module']['_id'] = $request[1];
					$undelete['error'] = 'modulo inesistente';
				}
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($undelete);
				break;
			case "archive":
				$data = $module->get($request[1]);
				if ($data['_id'] == $request[1] and $data['type'] == 'module_template') {
					$archive = $module->archiveTemplate($request[1]);
				} else {
					$archive['module'] = $data;
					$archive['module']['_id'] = $request[1];
					$archive['error'] = 'modulo inesistente';
				}
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($archive);
				break;
			case "archive_restore":
				$data = $module->get($request[1]);
				if ($data['_id'] == $request[1] and $data['type'] == 'module_template') {
					$archive = $module->restoreArchiveTemplate($request[1]);
				} else {
					$archive['module'] = $data;
					$archive['module']['_id'] = $request[1];
					$archive['error'] = 'modulo inesistente';
				}
				header('Content-type: application/json; charset=utf-8');
				echo json_encode($archive);
				break;
			default:

				break;
			}
			break;
		}
		/*}}}*/
		break;
	case 'DELETE':
		/*{{{ */
		header('Content-type: application/json; charset=utf-8');
		echo json_encode($module->softDelete($request[1]));
		/*}}}*/
		break;
}

?>

